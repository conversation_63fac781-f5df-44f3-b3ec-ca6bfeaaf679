{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\"id\":\"chatcmpl-fb823008-02b8-9146-80f9-c3427c57e9ef\",\"choices\":[{\"finish_reason\":\"stop\",\"index\":0,\"logprobs\":null,\"message\":{\"content\":\"聊天内容：罗皓文+初一+佛山孩子上周日回来情绪很大，哭着说心里不舒服，问什么事他不说，说我要死了，说我要跳楼，哭完又去上学了后面找到原因了吗？没有好的 那我们和孩子的沟通 确实是需要提升的您下午 2--5点期间什么时间方便，我具体和您电话沟下孩子情况 ~正常的 我2;30左右联系您大 A股的利好的新闻真的假的项昊能 大家都看到新闻你自己品留下东西再说准备收割一波诱多出货今天绿的跟翡翠一样就等三千保卫战\",\"refusal\":null,\"role\":\"assistant\",\"audio\":null,\"function_call\":null,\"tool_calls\":null}}],\"created\":1741764782,\"model\":\"qwen-vl-plus\",\"object\":\"chat.completion\",\"service_tier\":null,\"system_fingerprint\":null,\"usage\":{\"completion_tokens\":141,\"prompt_tokens\":2504,\"total_tokens\":2645,\"completion_tokens_details\":null,\"prompt_tokens_details\":null}}\n"]}], "source": ["import os\n", "from openai import OpenAI\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv()\n", "\n", "client = OpenAI(\n", "    # 若没有配置环境变量，请用百炼API Key将下行替换为：api_key=\"sk-xxx\",\n", "    api_key=os.getenv(\"DASHSCOPE_API_KEY\"),\n", "    base_url=\"https://dashscope.aliyuncs.com/compatible-mode/v1\",\n", ")\n", "completion = client.chat.completions.create(\n", "    model=\"qwen-vl-plus\",  # 此处以qwen-vl-plus为例，可按需更换模型名称。模型列表：https://help.aliyun.com/zh/model-studio/getting-started/models\n", "    messages=[{\"role\": \"user\",\"content\": [\n", "            {\"type\": \"text\",\"text\": \"\"\"\n", "将图片中的聊天提取出来，直接输出聊天的原始数据，不需要解释，不需要输出聊天的用户。\n", "输出格式：\n", "聊天内容\n", "聊天内容\n", "\"\"\"},\n", "            {\"type\": \"image_url\", \"image_url\": {\"url\": \"https://bailian-bmp-prod.oss-cn-beijing.aliyuncs.com/model_offline_result/10789674/1741764620287/qianwen/%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250312074345.jpg?Expires=1741765221&OSSAccessKeyId=STS.NT9Kg9NiYnBKBjD1abeCoSE5h&Signature=ehAb7JDBzoSqWu72Yu9revcC4ZE%3D&security-token=CAIS2AJ1q6Ft5B2yfSjIr5eMAN2No7Z42YCgQEz11WE3acxDvICeijz2IHhMenRoAu8fv%2FU1nmlQ6%2FsZlrp6SJtIXleCZtF94oxN9h2gb4fb415WLQG00s%2FLI3OaLjKm9u2wCryLYbGwU%2FOpbE%2B%2B5U0X6LDmdDKkckW4OJmS8%2FBOZcgWWQ%2FKBlgvRq0hRG1YpdQdKGHaONu0LxfumRCwNkdzvRdmgm4NgsbWgO%2Fks0CD0w2rlLFL%2BdugcsT4MvMBZskvD42Hu8VtbbfE3SJq7BxHybx7lqQs%2B02c5onDXgEKvEzXYrCOq4UycVRjE6IgHKdIt%2FP7jfA9sOHVnITywgxOePlRWjjRQ5ql0E4ehBQP3yBTn9%2FVTJeturjnXvGd24i0vOKnrXBkUe6AO61RyO0duyBbEKXTHHqgjCltxdZ73ok%2B15t8xV8qVRImjfXH7z0xtU4m5j29NCtAXxqAAQsHqIuJSpu6tw1z9FXAI%2FwuKNgJa6Ooj7Izr7jGTdWiMYP0cMHah1BnY863f4%2F7uhUilbDvjzkcZO2IVYQFB38RPrBd0hO1q0xrP2lON95t5nZsWiOeRkrLOjsV%2FffWuYUvslDyfISV5lbPQ2CX0ZlNb5evk2%2Bwqwkf54kc2PL8IAA%3D\"}},\n", "            {\"type\": \"image_url\", \"image_url\": {\"url\": \"https://bailian-bmp-prod.oss-cn-beijing.aliyuncs.com/model_offline_result/10789674/1741764712189/qianwen/%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250311140123.jpg?Expires=1741765312&OSSAccessKeyId=STS.NUhbKtLH5xN9SWfrzmQeFZBes&Signature=UinN3CAR3RQF9MF3ESXeMlx%2Bstw%3D&security-token=CAIS2AJ1q6Ft5B2yfSjIr5bdKfHAoZcUz4zSUXHXlno4XepqtYfOkTz2IHhMenRoAu8fv%2FU1nmlQ6%2FsZlrp6SJtIXleCZtF94oxN9h2gb4fb4zAHDAK00s%2FLI3OaLjKm9u2wCryLYbGwU%2FOpbE%2B%2B5U0X6LDmdDKkckW4OJmS8%2FBOZcgWWQ%2FKBlgvRq0hRG1YpdQdKGHaONu0LxfumRCwNkdzvRdmgm4NgsbWgO%2Fks0CD0w2rlLFL%2BdugcsT4MvMBZskvD42Hu8VtbbfE3SJq7BxHybx7lqQs%2B02c5onDXgEKvEzXYrCOq4UycVRjE6IgHKdIt%2FP7jfA9sOHVnITywgxOePlRWjjRQ5ql0E4ehBQP3yBTn9%2FVTJeturjnXvGd24jU%2B3OG1XBkUe6AO61RyO0duyBbEKXTHHqgjCltxdZ73ok%2B15t8xV8qVRImjfXH7z0xtU7aTUmnNCtAXxqAAV2VygG9R9GHaddsbNDg8uIpNhbHRk%2FKt9blh47rMOgB%2F%2FitxomT7d8AuxZcywAKFTs7EKsdhlyNQ%2FJ3wJ8HoJQJmfEEzwo2n2sPHDxmjwJd8igjJd9c8spf%2Fg102VLGY5RAGYSnDI6%2FCxddolZbDaq3aUV%2FgGpheGoJO4NrCDtqIAA%3D\"}},\n", "            ]}]\n", "    )\n", "print(completion.model_dump_json())"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["聊天内容：罗皓文+初一+佛山孩子上周日回来情绪很大，哭着说心里不舒服，问什么事他不说，说我要死了，说我要跳楼，哭完又去上学了后面找到原因了吗？没有好的 那我们和孩子的沟通 确实是需要提升的您下午 2--5点期间什么时间方便，我具体和您电话沟下孩子情况 ~正常的 我2;30左右联系您大 A股的利好的新闻真的假的项昊能 大家都看到新闻你自己品留下东西再说准备收割一波诱多出货今天绿的跟翡翠一样就等三千保卫战\n"]}], "source": ["print(completion.choices[0].message.content)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 2}
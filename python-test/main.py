import http.client
import json
import os
from openpyxl import load_workbook
import time
import datetime
import logging
logging.basicConfig(level=logging.INFO,
                    format="%(asctime)s %(name)s %(levelname)s %(message)s",
                    datefmt = '%Y-%m-%d  %H:%M:%S %a',   
                    filename='myapp.log',
                    filemode='w'
                    )


def check(caseId, session, cookie):
    conn = http.client.HTTPSConnection("aps.sz.creditcard.ecitic.com")
    payload = json.dumps({
       "start": 1,
       "limit": 30,
       "queryObj": "{\"custId\":\"" + caseId + "\",\"lastPayDate\":[\"\",\"\"],\"lastPayDateStart\":\"\",\"lastPayDateEnd\":\"\"}"
    })
    headers = {
       'Channelid': 'IPCC_OUT',
       'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.95 Safari/537.36',
       'Pageid': '7151',
       'Store-Session': session,
       'Cookie': cookie,
       'Content-Type': 'application/json',
       'Accept': '*/*',
       'Host': 'aps.sz.creditcard.ecitic.com',
       'Connection': 'keep-alive'
    }
    conn.request("POST", "/aps-common-gateway/aps-fuse-gateway/aps-openapi-5g/aps-openapi-5g/phone-collect-case/list", payload, headers)
    res = conn.getresponse()
    data = res.read()
    res_json = json.loads(data.decode("utf-8"))
    if res_json['retCode'] != '0' or len(res_json['data']) <= 0:
        logging.info(res_json)
        return False
    return True
    
def remark(caseId, remark, session, cookie):
    appointDate = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime('%Y%m%d')
    conn = http.client.HTTPSConnection("aps.sz.creditcard.ecitic.com")
    payload = json.dumps({
       "caseId": caseId,
       "appointDate": appointDate,
       "taskTypeId": "#wo#",
       "ipccTaskTypeId": "#wo#",
       "appointTime": "0000",
       "actionCodeName": "SSMS",
       "comment": remark,
       "collType": "E",
       "contactType": "A",
       "acctId": "",
       "dueAmt": "",
       "modelId": "TaskPerspective",
       "curTimestamp": int(round(time.time() * 1000)),
       "callType": "O",
       "callSrcNumber": "",
       "callDestNumber": "",
       "complainInfo": {
          "acomplain": False,
          "complainIdList": [],
          "autoHandleActionCode": False,
          "caseId": caseId,
          "collType": "E",
          "contactType": "A",
          "actionCodeName": "SSMS",
          "appointDate": appointDate,
          "appointTime": "0000",
          "comment": remark
       }
    })
    headers = {
       'Channelid': 'IPCC_OUT',
       'Store-Session': session,
       'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.95 Safari/537.36',
       'Pageid': '7151',
       'Cookie': cookie,
       'Content-Type': 'application/json',
       'Accept': '*/*',
       'Host': 'aps.sz.creditcard.ecitic.com',
       'Connection': 'keep-alive'
    }
    conn.request("POST", "/aps-common-gateway/aps-fuse-gateway/aps-openapi-5g/aps-openapi-5g/action/submit", payload, headers)
    res = conn.getresponse()
    data = res.read()
    res_json = json.loads(data.decode("utf-8"))
    if res_json['retCode'] != '0':
        logging.info(res_json)
        return False
    return True
    
if __name__ == '__main__':
    root = os.getcwd()
    with open(root + '/config.txt', 'r') as f:
        contents = f.readlines()
    config = {}
    for content in contents:
        arr = content.split("==")
        config[arr[0]] = arr[1].replace('\n', '').replace('\r', '')
    filename = '%s/刷.xlsx' % root
    workbook = load_workbook(filename=filename)
    
    total_row_num = 0
    for sheet in workbook:
        total_row_num += sheet.max_row

    row_num = 0
    for sheet in workbook:
        for row in sheet.rows:
            caseId = row[0].value
            if caseId != None:
                try:
                    if check(caseId, config['Store-Session'], config['Cookie']):
                        remark(caseId, row[1].value, config['Store-Session'], config['Cookie'])
                except:
                    logging.warning("Unexpected error:", sys.exc_info()[0])
                    logging.warning('[' + caseId + '] error')
            row_num += 1
            print('====' + str(int(row_num * 100 / total_row_num)) + '%====')

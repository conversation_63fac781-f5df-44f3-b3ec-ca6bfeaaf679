{"cells": [{"cell_type": "code", "execution_count": 1, "id": "359bb179-030c-451d-a549-d9d082eeb375", "metadata": {}, "outputs": [], "source": ["from sqlmodel import select, text, create_engine, Field, Session, SQLModel\n", "from pgvector.sqlalchemy import Vector\n", "from sqlalchemy import Column\n", "from pydantic import ConfigDict\n", "\n", "DATABASE_URL = \"postgresql://fuying_pg:#Fuying#2021#@pgm-bp1j698rf1n2nq7rdo.rwlb.rds.aliyuncs.com:5432/fuying_vector\"\n", "\n", "engine_save = create_engine(\n", "    DATABASE_URL,\n", "    echo=False, future=True\n", ")\n", "\n", "class File(SQLModel, table=True):\n", "    model_config = ConfigDict(arbitrary_types_allowed=True)\n", "    __tablename__ = \"llm_media\"\n", "    \n", "    id: int | None = Field(default=None, primary_key=True)\n", "    title: str\n", "    url: str\n", "\n", "class Data(SQLModel, table=True):\n", "    model_config = ConfigDict(arbitrary_types_allowed=True)\n", "    __tablename__ = \"llm_media_relation\"\n", "    \n", "    id: int | None = Field(default=None, primary_key=True)\n", "    file_id: int\n", "    title: str\n", "    embedding: Vector = Field(sa_column=Column(Vector(1024)))"]}, {"cell_type": "code", "execution_count": 17, "id": "1d7aee79", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\software\\python\\Lib\\site-packages\\IPython\\core\\interactiveshell.py:3517: UserWarning: WARNING! response_format is not default parameter.\n", "                response_format was transferred to model_kwargs.\n", "                Please confirm that response_format is what you intended.\n", "  if await self.run_code(code, result, async_=asy):\n"]}], "source": ["from langchain_community.embeddings import DashScopeEmbeddings\n", "import os\n", "from dotenv import load_dotenv\n", "from langchain_openai import ChatOpenAI\n", "import os\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_core.output_parsers import StrOutputParser\n", "\n", "load_dotenv()\n", "\n", "embeddings = DashScopeEmbeddings(\n", "    dashscope_api_key=os.getenv('DASHSCOPE_API_KEY'),\n", "    model=\"text-embedding-v4\",\n", "    # other params...\n", ")\n", "\n", "llm = ChatOpenAI(\n", "    api_key=\"sk-5d2090a0ac394b218376b67cb91123f5\",\n", "    base_url=os.getenv('MODAL_URL'),\n", "    model='qwen-plus',\n", "    response_format={\"type\": \"json_object\"}\n", ")\n", "prompt = ChatPromptTemplate.from_messages([\n", "    ('system', \"\"\"\n", "## 任务\n", "- 根据用户输入的文章，从不同的角度提取出三个文章想要解决的问题。\n", "     \n", "## 限制:\n", "- 提出的问题必须切合文章的核心思想。 \n", "- 提出的问题必须和文章想要解决的问题强相关。\n", "- 每个问题长度30个字左右。\n", "     \n", "## 返回JSON格式\n", "[\"问题1\",\"问题2\",\"问题3\"]\n", "\"\"\"),\n", "    ('user', '{input}')\n", "])\n", "# prompt = ChatPromptTemplate.from_messages([\n", "#     ('system', \"\"\"\n", "# 你是一共聊天助手\n", "# \"\"\"),\n", "#     ('user', '{input}')\n", "# ])\n", "output_parser = StrOutputParser()\n", "chain = prompt | llm | output_parser"]}, {"cell_type": "code", "execution_count": 2, "id": "ff05db1f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[-0.03399784117937088, -0.06905519217252731, 0.009816058911383152, -0.050420261919498444, 0.018338890746235847, -0.016858691349625587, 0.07958798110485077, 0.038578666746616364, -0.03353041037917137, 0.08787709474563599, 0.035400133579969406, -0.01439688540995121, 0.08314045518636703, 0.05624757334589958, -0.0019875571597367525, -0.023714350536465645, -0.018899807706475258, -0.02818611077964306, -0.013103659264743328, 0.0333746001124382, -0.01766890473663807, 0.017232635989785194, 0.028980743139982224, -0.0349327027797699, 0.024010390043258667, -0.010969055816531181, -0.02506990171968937, 0.03998096287250519, 0.0034278298262506723, -0.038578666746616364, -0.026970788836479187, -0.0315827801823616, 0.023807836696505547, -0.0018064273754134774, -0.00474832346662879, 0.019928157329559326, -0.0013701581628993154, 0.02650335803627968, 0.014155379496514797, -0.021268127486109734, 0.006357066333293915, -0.007404891774058342, 0.0006013309466652572, -0.005566328298300505, 0.0183856338262558, -0.002613720251247287, -0.024384336546063423, -0.014116426929831505, 0.010898941196501255, -0.02655010111629963, -0.001011794083751738, -0.009738152846693993, -0.014513744041323662, -0.00042531383223831654, -0.030445361509919167, -0.008086562156677246, -0.012971220538020134, 0.016422420740127563, 0.01067301630973816, 0.012566112913191319, -0.007116642314940691, 0.08021122217178345, -0.029292365536093712, 0.0027812165208160877, 0.10464230179786682, 0.02425968647003174, -0.012667389586567879, 0.0610465332865715, 0.010190003551542759, -0.052445799112319946, 0.0025182864628732204, -0.01782471500337124, 0.03742567077279091, 0.024742700159549713, -0.028778189793229103, 0.005951959174126387, -0.013571090064942837, -0.023496216163039207, -0.02052023634314537, 0.025708723813295364, 0.0015181780327111483, -0.05521922558546066, 0.005570223554968834, -0.0478961318731308, 0.045808274298906326, 0.028871675953269005, -0.005468946881592274, 0.036428485065698624, -0.010299070738255978, 0.10937893390655518, -0.0028669124003499746, -0.011646831408143044, 0.027142180129885674, 0.0388902872800827, -0.023402730002999306, 0.07840381562709808, -0.012628437019884586, -0.016251029446721077, 0.009029216133058071, 0.029650729149580002, -0.034434109926223755, -0.03748799487948418, 0.008740966208279133, 0.04926726594567299, 0.03670894354581833, 0.04577711224555969, -0.016048476099967957, -0.04325298219919205, -0.01648474670946598, -0.021018829196691513, -0.001442220527678728, -0.06419390439987183, 0.03589872643351555, 0.02424410544335842, 0.030414199456572533, 0.005823415704071522, 0.015261633321642876, 0.01985025219619274, 0.0193984005600214, 0.08557110279798508, 0.0031765855383127928, -7.4976173891627695e-06, 0.07148583233356476, -0.03108418546617031, -0.00010383305925643072, -0.006399914156645536, -0.0038270943332463503, -0.011444278061389923, 0.008577365428209305, -0.0075918640941381454, -0.037238698452711105, -0.03399784117937088, 0.007003679871559143, -0.0043393210507929325, 0.005172906909137964, -0.05870937928557396, -0.022592514753341675, 0.026269642636179924, -0.07859079539775848, -0.019413981586694717, -0.026207318529486656, -0.009278512559831142, 0.04322182014584541, -0.01271413266658783, -0.10969055444002151, 0.0021268127020448446, -0.04116512089967728, -0.027500545606017113, -0.01874399743974209, 0.014116426929831505, -0.0719844251871109, 0.009901754558086395, -0.02363644540309906, 0.035462457686662674, -0.0031415282282978296, -0.032439734786748886, 0.006061026360839605, 0.02910539321601391, 0.04549665004014969, -0.03390435501933098, 0.0005097922985441983, 0.032907165586948395, -0.018541444092988968, 0.04262974113225937, 0.013625623658299446, -0.05724475905299187, 0.0944211333990097, -0.01529279537498951, 0.010314651764929295, 0.005180697422474623, -0.010782083496451378, 0.022592514753341675, -0.0001637226960156113, 0.03761264309287071, -0.015970570966601372, -0.012137633748352528, 0.03617918863892555, 0.040666528046131134, 0.0036089594941586256, -0.025599656626582146, -0.0046937898732721806, 0.01339969877153635, -0.03169184550642967, -0.03184765577316284, 0.012246701866388321, -0.02103441022336483, 0.053380660712718964, 0.042193468660116196, 0.006695954129099846, 0.031099766492843628, -0.006396018899977207, 0.02276390790939331, 0.018183080479502678, 0.012706342153251171, -0.008873404935002327, -0.04088466241955757, -0.013765853829681873, 0.06737244129180908, 0.12265399098396301, -0.01519151870161295, -0.018354471772909164, -0.01519151870161295, -0.005597490351647139, -0.02114347741007805, 0.013337374664843082, -0.00965245719999075, 0.01314261183142662, -0.012908895500004292, 0.036989402025938034, -0.013485394418239594, -0.014155379496514797, -0.014685135334730148, 0.008998053148388863, -0.020302101969718933, 0.009403160773217678, -0.000475708773592487, -0.002045012079179287, 0.009512227959930897, 0.02843540720641613, 0.013134821318089962, 0.024882929399609566, -0.014965593814849854, -0.015643369406461716, 0.017030082643032074, -0.03136464208364487, 0.026378709822893143, -0.04278555139899254, 0.010984636843204498, 0.006154512986540794, 0.01924259029328823, -0.01071975938975811, -0.003692707745358348, -0.08656828850507736, -0.019819090142846107, 0.015666740015149117, 0.009520018473267555, 0.04125860705971718, -0.000758602109272033, 0.02572430483996868, -0.01883748359978199, 0.010166632011532784, 0.05422203615307808, 0.025958022102713585, 0.00038124871207401156, 0.007930751889944077, 0.0018064273754134774, -0.023605283349752426, 0.012971220538020134, -0.010914522223174572, 0.015355120413005352, -0.025210130959749222, -0.01843237690627575, 0.01513698510825634, -0.01688985340297222, -0.008569574914872646, 0.008281325921416283, 0.0043237400241196156, 0.011942870914936066, -0.01077429298311472, 0.03465224429965019, 0.0027266829274594784, 0.04967237263917923, -0.014147588983178139, -0.04823891445994377, -0.020769532769918442, -0.004054966848343611, 0.012628437019884586, -0.00860073696821928, -0.03876563906669617, 0.00717507116496563, -0.00013986421981826425, 0.025256874039769173, 0.010003031231462955, -0.03574291616678238, -0.06942913681268692, -0.024181781336665154, -0.10302186757326126, -0.04057304188609123, -0.026004765182733536, -0.02210950292646885, -0.022982042282819748, -0.030242808163166046, 0.03798658773303032, 0.006053235847502947, 0.005718243774026632, 0.054751791059970856, -0.0006617074832320213, -0.004128977190703154, 0.018447957932949066, 0.03002467378973961, -0.032190438359975815, 0.0698654055595398, -0.004265311174094677, -0.06313439458608627, -0.025537332519888878, 0.018401214852929115, 0.05294439196586609, 0.01724821701645851, -0.03200346603989601, 0.013493184931576252, -0.04222463071346283, 0.00992512609809637, -0.04537200182676315, -0.043626926839351654, 0.010727549903094769, 0.01598615199327469, -0.022296475246548653, -0.029432594776153564, 0.0024579099845141172, -0.06681152433156967, -0.037082888185977936, 0.03798658773303032, -0.029946768656373024, 0.04253625497221947, -0.01746635138988495, -0.017887039110064507, 0.003992642741650343, -0.02832634001970291, 0.0054650516249239445, -0.028980743139982224, -0.02006838656961918, -0.013134821318089962, -0.006972517818212509, 0.004993725102394819, 0.004962563049048185, -0.011319628916680813, -0.002089807763695717, -0.014459209516644478, 0.013586671091616154, -0.0515732578933239, -0.03384203091263771, -0.004370483104139566, -0.005850682500749826, 0.02623848058283329, -0.004296473227441311, 0.03393551707267761, -0.00885782390832901, 0.003221380989998579, 0.005406622774899006, 0.0313490629196167, -0.0023819522466510534, 0.061638616025447845, -0.0031103661749511957, 0.054595980793237686, 0.07553690671920776, -0.06780871003866196, -0.0008783814264461398, -0.0009820928098633885, 0.007054318208247423, -0.02475828118622303, 0.005632547661662102, 0.0088032903149724, 0.0014909112360328436, 0.07672106474637985, 0.016360096633434296, -0.05397273972630501, 0.03189440071582794, -0.003073361236602068, -0.07921403646469116, 0.01720147393643856, -0.05687081441283226, 0.03247089684009552, -0.039233073592185974, -0.00696862256154418, 0.0057533010840415955, 0.009231769479811192, 0.010330232791602612, 0.020099548622965813, 0.023418311029672623, 0.009808268398046494, 0.05095001682639122, -0.004214672837406397, 0.030086997896432877, 0.006731011439114809, -0.001445141970179975, 0.03293832764029503, 0.04396970942616463, -0.04630686715245247, -0.0003503300540614873, 0.003129842458292842, 0.047709159553050995, -0.03736334666609764, 0.025054320693016052, 0.04337763041257858, 0.037082888185977936, -0.00643107620999217, -0.028762608766555786, 0.029790958389639854, -0.00736593920737505, -0.02996234968304634, 0.008663061074912548, -0.007946332916617393, 0.007252976298332214, -0.013134821318089962, 0.03966934233903885, -0.0041757202707231045, -0.009449903853237629, -0.0016175071941688657, 0.05964424088597298, 0.005149535369127989, 0.007529539987444878, 0.006481715012341738, -0.018930969759821892, 0.051884882152080536, -0.005017096642404795, -0.006618048995733261, 0.04671197384595871, 0.010524995625019073, -0.014459209516644478, 0.0046275705099105835, 0.015222680754959583, -0.018666092306375504, 0.10414370894432068, 0.05063839629292488, -0.01607963815331459, -0.0031123138032853603, -0.004292577970772982, -0.022374380379915237, 0.011187190189957619, -0.006364856846630573, -0.06724779307842255, -0.04717940464615822, -0.007630816660821438, -0.013641204684972763, -0.04100931063294411, 0.12620647251605988, -0.010649644769728184, 0.016453582793474197, 0.052352312952280045, -0.0007074768072925508, 0.00524691678583622, 0.003244752762839198, -0.004639256279915571, -0.029790958389639854, 0.032346248626708984, -0.003048042068257928, 0.05472062900662422, -0.03240857273340225, -0.0012406407622620463, -0.02706427499651909, -0.04926726594567299, 0.023854579776525497, -0.01827656663954258, -0.009870592504739761, -0.004619779996573925, 0.01360225211828947, 0.035711754113435745, -0.04390738531947136, -0.01991257630288601, 0.011888337321579456, 0.036366160959005356, -0.030866051092743874, 0.05366111919283867, 0.00343367294408381, 0.023355986922979355, 0.01603289507329464, 0.035150837153196335, -0.05721359699964523, -0.05581130459904671, 0.014817574061453342, 0.06755941361188889, -0.03935772180557251, -0.06419390439987183, 0.004935296252369881, 0.006376542616635561, 0.01946072466671467, 0.034589920192956924, 0.024727119132876396, 0.03078814595937729, -0.0404483936727047, 0.01161566935479641, -0.007899589836597443, -0.001343865180388093, -0.03123999573290348, 0.01746635138988495, -0.03536897152662277, -0.027391476556658745, 0.0004136280622333288, 0.008725385181605816, -0.0009129518875852227, -0.0031317900866270065, 0.0018765421118587255, -0.046587325632572174, 0.006547934375703335, -0.048270076513290405, -0.03745683282613754, 0.013968407176434994, -0.018105175346136093, -0.004908029455691576, -0.001846353872679174, -0.031099766492843628, -0.01590045727789402, 0.02067604660987854, 0.05839775875210762, -0.06824497878551483, 0.005800044164061546, -0.08257953822612762, 0.017575418576598167, 0.008873404935002327, 0.01472408790141344, -0.013859339989721775, 0.005578014068305492, -8.119185076793656e-05, -0.03976282849907875, 0.011343000456690788, 0.004923610482364893, -0.050825368613004684, 0.0352131612598896, 0.009987450204789639, -0.03349924832582474, -0.07036399841308594, 0.005940273404121399, -0.048924483358860016, 0.006111665163189173, 0.027282409369945526, 0.012947848998010159, -0.022545771673321724, 0.04839472472667694, -0.012682970613241196, -0.017450770363211632, -0.04636919125914574, -0.0003118643653579056, 0.025397103279829025, 0.015277214348316193, -0.02760961279273033, 0.006984203588217497, -0.00041776677244342864, -0.009691409766674042, 0.07983727753162384, 0.01960095390677452, 0.014272237196564674, -0.023511797189712524, 0.0032467003911733627, 0.01832330971956253, -0.06282277405261993, 0.04761567339301109, 0.00975373387336731, -0.026113832369446754, 0.03742567077279091, -0.007272453047335148, 0.032502058893442154, -0.01755983754992485, 0.017279379069805145, -0.0040510715916752815, -0.015043498948216438, -0.008086562156677246, 0.015456397086381912, -0.048924483358860016, 0.031723007559776306, -0.0049820393323898315, -0.027936814352869987, 0.010953474789857864, 0.025178968906402588, -0.053879253566265106, 0.016375677660107613, -0.016297772526741028, -0.05110582709312439, 0.01590045727789402, 0.0008335859165526927, -0.06238650530576706, 0.00855399388819933, -0.015199309214949608, -0.019616536796092987, -0.006407704669982195, -0.01894655078649521, -0.04303484782576561, -0.015884876251220703, -0.011117075569927692, -0.06980308145284653, -0.01409305538982153, -0.033312276005744934, 0.04453062638640404, 0.04599524661898613, 0.021455099806189537, -0.0071906521916389465, 0.04487340897321701, -0.012893314473330975, 0.027095437049865723, 0.0977243185043335, 0.02098766714334488, -0.023979227989912033, -0.0203644260764122, -0.0027831641491502523, 0.040199097245931625, 0.03228392452001572, -0.007841160520911217, -0.014280027709901333, 0.01879074051976204, 0.0663752555847168, 0.06787103414535522, 0.001476304023526609, 0.010267908684909344, -0.039638180285692215, 0.0074944826774299145, -0.039638180285692215, 0.0405418798327446, -0.01674962416291237, -0.031115347519516945, -0.022888556122779846, 0.030850470066070557, -0.04686778411269188, -0.034122489392757416, 0.02291971817612648, 0.036833591759204865, 0.011218352243304253, -0.01832330971956253, 0.0156511589884758, 0.020457912236452103, 0.004802857059985399, -0.0443124920129776, 0.02108115330338478, 0.014918850734829903, 0.04016793519258499, -0.005800044164061546, -0.007210128474980593, 0.0013857391895726323, -0.01435014232993126, 0.01883748359978199, -0.007132223341614008, -0.028061462566256523, -0.009130492806434631, -0.006053235847502947, 0.004783380776643753, 0.05422203615307808, -0.007408787030726671, -0.029697472229599953, 0.022841813042759895, 0.010680806823074818, 0.04717940464615822, 0.0051612211391329765, 0.02148626185953617, 0.02439991757273674, -0.04041723161935806, 0.03945120796561241, 0.0294637568295002, -0.005648128688335419, -0.007599654607474804, 0.00706989923492074, -0.04733521491289139, -0.030616754665970802, 0.0331876277923584, 0.017232635989785194, -0.0057221390306949615, -0.03508851304650307, -0.019273752346634865, -0.01623544842004776, -0.03711405023932457, -0.047958455979824066, 0.02690846472978592, -0.012581693939864635, 0.014685135334730148, 0.03982515260577202, 0.009481065906584263, 0.026378709822893143, -0.006458343006670475, -0.007630816660821438, 0.002652673050761223, -0.001808375003747642, -0.0018804373685270548, -0.010641854256391525, -0.031878817826509476, 0.016921015456318855, 0.003231119131669402, -0.011498811654746532, 0.014599439688026905, -0.021377194672822952, 0.0249140914529562, 0.0030811517499387264, 0.059550754725933075, 0.03123999573290348, -0.06207488477230072, -0.013220516964793205, 0.019117942079901695, 0.008250163868069649, -0.006540143862366676, 0.017326122149825096, -0.004136767704039812, 0.03406016528606415, -0.0013613938353955746, 0.013290631584823132, -0.02746938355267048, 0.021455099806189537, -0.03745683282613754, 0.007369834464043379, -0.0845739096403122, -0.003205799963325262, -0.020099548622965813, 0.044187843799591064, 0.028965162113308907, -0.010945684276521206, -0.005001515615731478, -0.023200176656246185, -0.029790958389639854, 0.01935165748000145, 0.07210907340049744, -0.0004910464049316943, -0.038827963173389435, 0.0313023179769516, 0.037082888185977936, 0.036023374646902084, -0.03016490302979946, -0.023465054109692574, -0.048768673092126846, -0.014583858661353588, 0.022950880229473114, -0.0046275705099105835, 0.011748108081519604, -0.02062930352985859, 0.01191949937492609, -0.026814978569746017, -0.010602901689708233, 0.013680157251656055, -0.03421597555279732, 0.009278512559831142, -0.0038173559587448835, 0.07385415583848953, 0.02966631017625332, -0.011529973708093166, 0.0007527592242695391, -0.006096084136515856, 0.025210130959749222, 0.025085482746362686, -0.014225494116544724, -0.0078255794942379, 0.040510717779397964, 0.013804806396365166, 0.00895910058170557, 0.0010108202695846558, -0.0016301668947562575, 0.036521971225738525, -0.0554373599588871, -0.029230041429400444, -0.028715865686535835, 0.0982852354645729, 0.03733218461275101, 0.0022202988620847464, -0.053785767406225204, 0.011997404508292675, -0.0110781230032444, -0.028824932873249054, -0.03186323866248131, -0.017575418576598167, -0.03222160041332245, -0.009948497638106346, 0.09741269797086716, 0.03119325265288353, 0.008904566988348961, 0.05752521753311157, 0.044343654066324234, -0.005480632651597261, -0.04213114455342293, 0.003918632864952087, 0.01924259029328823, -0.001599978539161384, -0.026674749329686165, 0.027017531916499138, 0.0442190058529377, -0.020816275849938393, -0.027594031766057014, 0.020753951743245125, 0.028450988233089447, -0.046680811792612076, 0.023776674643158913, -0.03181649371981621, 0.016827529296278954, 0.01812075637280941, 0.024820605292916298, 0.021501842886209488, -0.026207318529486656, 0.02000606246292591, -0.006902403198182583, 0.013056916184723377, 0.009278512559831142, 0.033561572432518005, 0.002068383852019906, 0.008966891095042229, 0.026098251342773438, 0.00106243253685534, -0.020099548622965813, 0.01980350911617279, -0.02874702773988247, 0.008187838830053806, -0.1219060942530632, -0.007993075996637344, 0.02168881520628929, 0.026098251342773438, -0.00032866265974007547, -0.00923955999314785, 0.05303787812590599, -0.028824932873249054, 0.021221384406089783, 0.03689591586589813, 0.03114650957286358, 0.0032077475916594267, 0.005566328298300505, -0.018619349226355553, -0.02803030051290989, 0.04920494183897972, 0.02628522366285324, -0.029167717322707176, 0.0333746001124382, 0.015916038304567337, 0.0030597278382629156, -0.03845401853322983, 0.009504437446594238, 0.015760228037834167, -0.02834192104637623, 0.03155161812901497, -0.01355550903826952, -0.05793032422661781, -0.033966679126024246, 0.02951049990952015, 0.013002382591366768, 0.0036245405208319426, 0.03268903121352196, 0.007400996517390013, -0.05687081441283226, -0.0018658301560208201, 0.01071975938975811, -0.020644884556531906, -0.023293662816286087, 0.02502315863966942, -0.0010234798537567258, -0.018183080479502678, -0.013103659264743328, 0.0002604956098366529, -0.002383899874985218, 0.025568494573235512, -0.02399480901658535, -0.01013546995818615, -0.036677781492471695, -0.037706129252910614, 0.0515732578933239, 0.010867779143154621, -0.009481065906584263, -0.003682969603687525, -0.017326122149825096, -0.02175113931298256, -0.009099330753087997, -0.05877170339226723, -0.059083323925733566, 0.014638392254710197, -0.03268903121352196, -0.00982384942471981, 0.04057304188609123, -0.013407489284873009, 0.08133305609226227, -0.02612941339612007, 0.021330451592803, 0.01935165748000145, 0.03960701823234558, -0.035400133579969406, -0.005652023945003748, 0.02323133870959282, 0.005784463137388229, -0.0034219869412481785, 0.005652023945003748, 0.032034628093242645, 0.017700066789984703, -0.010743130929768085, -0.007564597297459841, 0.018510282039642334, -0.004288682714104652, -0.012877733446657658, 0.008367021568119526, -0.006614153739064932, 0.012309025973081589, -0.009613504633307457, -0.008561784401535988, 0.014381304383277893, 0.011826013214886189, 0.04932959005236626, -0.032190438359975815, 0.039638180285692215, 0.029136555269360542, -0.022327637299895287, -0.009839430451393127, -0.0023761093616485596, 0.02363644540309906, -0.032502058893442154, 0.01319714542478323, 0.021127896383404732, -0.052757419645786285, 0.0037102364003658295, 0.06456784904003143, 0.032595545053482056, -0.001751893782056868, -0.020457912236452103, -0.0014334562001749873, 0.028497731313109398, -0.01634451560676098, 0.009130492806434631, -0.0062713706865906715, 0.037643805146217346, 0.012986801564693451, -0.03209695219993591, -0.03209695219993591, 0.0333746001124382, -0.03190997987985611, -0.018307728692889214, 0.033468086272478104, 0.012410302646458149, -0.0050794207490980625, 0.014965593814849854, -0.0425674170255661, -0.03823588415980339, -0.048924483358860016, 0.014287818223237991, -0.027453800663352013, -0.052196502685546875, 0.007868427783250809, 0.010041983798146248, 0.008686432614922523, 0.034839216619729996, -0.02056697942316532, -0.007880114018917084, -0.019273752346634865, -0.05512573942542076, -0.010571738705039024, -0.017887039110064507, -0.010969055816531181, -0.0030714136082679033, 0.027812166139483452, 0.03920190781354904, 0.008530622348189354, 0.03945120796561241, 0.02771867997944355, -0.0019524997333064675, 0.04487340897321701, -0.03268903121352196, -0.005694872234016657, 0.01802726835012436, 0.049641210585832596, 0.0422869548201561, -0.022093921899795532, 0.011311838403344154, 0.005048258695751429, 0.007735989056527615, 0.01746635138988495, -0.032346248626708984, -0.010415928438305855, -0.032595545053482056, 0.05319368839263916, 0.010462671518325806, 0.046119894832372665, -0.03449643403291702, 0.01033802330493927, -0.020535817369818687, -0.03711405023932457, -0.01509024202823639, -0.0002529485209379345, -0.028887256979942322, -0.0005550747155211866, 0.010408137924969196, 0.004214672837406397, 0.0037199745420366526, 0.04104047268629074, 0.0010108202695846558, 0.0032622814178466797, -0.0025961915962398052, 0.03639732301235199, -0.009714781306684017, -0.02399480901658535, -0.01102358940988779, -0.013407489284873009, 0.009465484879910946, -0.0056286524049937725, 0.013913873583078384, -0.0072997198440134525, 0.05478295311331749, 0.00019658896781038493, -0.014170960523188114, 0.009948497638106346, -0.01883748359978199, -0.037550318986177444, -0.037550318986177444, -0.013368536718189716, -0.021205803379416466, -0.08775244653224945, -0.0024442763533443213, -0.020847437903285027, -0.0028376977425068617, 0.014568277634680271, -0.0069296699948608875, -0.0008559836423955858, -0.0067388019524514675, 0.02246786653995514, 0.007085480261594057, 0.0005020017852075398, 0.023574121296405792, 0.016204286366701126, -0.002393638016656041, 0.05712011083960533, 0.01541744451969862, 0.02190694957971573, 0.013477603904902935, -0.04848821088671684, 0.029292365536093712, 0.018759578466415405, 0.029790958389639854, 0.006337590049952269, 0.00786063726991415, -0.06406925618648529, -0.023667607456445694, 0.011631250381469727, 0.008468298241496086, -0.08407531678676605, 0.031053023412823677, -0.028373083099722862, -0.025552913546562195, -0.017497513443231583, -0.03384203091263771, -0.040105611085891724, 0.033156465739011765, 0.01212984323501587, -0.04957888647913933, -0.027936814352869987, -0.003873837413266301, -0.00855399388819933, 0.014061893336474895, -0.04727289080619812, 0.051074665039777756, 0.020489074289798737, 0.016328934580087662]\n"]}], "source": ["embedding = embeddings.embed_query(\"我什么时候参加的面试？\")\n", "print(embedding)"]}, {"cell_type": "code", "execution_count": 25, "id": "b2181773", "metadata": {}, "outputs": [{"data": {"text/plain": ["[(3368, '1.人为什么要自律？', 'https://video.fuyingy.com/customerTrans/da6849f0645f49892215e011f6092118/1760dc67-17302fba5c7-0004-ab9d-931-da694.mp4', '{\"file_url\":\"https://video.fuyingy.com/customerTrans/da6849f0645f49892215e011f6092118/1760dc67-17302fba5c7-0004-ab9d-931-da694.mp4\",\"properties\":{\"au ... (113251 characters truncated) ... n\":\"\"},{\"begin_time\":314080,\"end_time\":314270,\"text\":\"区\",\"punctuation\":\"\"},{\"begin_time\":314270,\"end_time\":314460,\"text\":\"别\",\"punctuation\":\"。\"}]}]}]}'),\n", " (3369, '2.短视频是如何让我们失控的？（上）', 'https://video.fuyingy.com/customerTrans/da6849f0645f49892215e011f6092118/bd1a73e-17302fd5f47-0004-ab9d-931-da694.mp4', '{\"file_url\":\"https://video.fuyingy.com/customerTrans/da6849f0645f49892215e011f6092118/bd1a73e-17302fd5f47-0004-ab9d-931-da694.mp4\",\"properties\":{\"aud ... (209871 characters truncated) ... n\":\"\"},{\"begin_time\":607421,\"end_time\":607620,\"text\":\"费\",\"punctuation\":\"\"},{\"begin_time\":607620,\"end_time\":607820,\"text\":\"了\",\"punctuation\":\"。\"}]}]}]}'),\n", " (3370, '3.短视频是如何让我们失控的？（下）', 'https://video.fuyingy.com/customerTrans/da6849f0645f49892215e011f6092118/373ee5e8-17302fe3955-0004-ab9d-931-da694.mp4', '{\"file_url\":\"https://video.fuyingy.com/customerTrans/da6849f0645f49892215e011f6092118/373ee5e8-17302fe3955-0004-ab9d-931-da694.mp4\",\"properties\":{\"au ... (168004 characters truncated) ... n\":\"\"},{\"begin_time\":506403,\"end_time\":506681,\"text\":\"人\",\"punctuation\":\"\"},{\"begin_time\":506681,\"end_time\":506960,\"text\":\"性\",\"punctuation\":\"。\"}]}]}]}'),\n", " (3371, '4.你的孩子为什么不自律？', 'https://video.fuyingy.com/customerTrans/da6849f0645f49892215e011f6092118/51ee84ab-17302fee4b2-0004-ab9d-931-da694.mp4', '{\"file_url\":\"https://video.fuyingy.com/customerTrans/da6849f0645f49892215e011f6092118/51ee84ab-17302fee4b2-0004-ab9d-931-da694.mp4\",\"properties\":{\"au ... (77998 characters truncated) ... n\":\"\"},{\"begin_time\":240564,\"end_time\":240802,\"text\":\"假\",\"punctuation\":\"\"},{\"begin_time\":240802,\"end_time\":241040,\"text\":\"象\",\"punctuation\":\"。\"}]}]}]}'),\n", " (3372, '5.父母做这三件事，孩子越来越自律！', 'https://video.fuyingy.com/customerTrans/da6849f0645f49892215e011f6092118/2236fbe5-17302ff16eb-0004-ab9d-931-da694.mp4', '{\"file_url\":\"https://video.fuyingy.com/customerTrans/da6849f0645f49892215e011f6092118/2236fbe5-17302ff16eb-0004-ab9d-931-da694.mp4\",\"properties\":{\"au ... (426618 characters truncated) ... \"},{\"begin_time\":1426257,\"end_time\":1426528,\"text\":\"孩\",\"punctuation\":\"\"},{\"begin_time\":1426528,\"end_time\":1426800,\"text\":\"子\",\"punctuation\":\"。\"}]}]}]}'),\n", " (3373, '6.让孩子拒绝诱惑最重要的是做到这3点！', 'https://video.fuyingy.com/customerTrans/da6849f0645f49892215e011f6092118/2c69f360-17302ff4dd9-0004-ab9d-931-da694.mp4', '{\"file_url\":\"https://video.fuyingy.com/customerTrans/da6849f0645f49892215e011f6092118/2c69f360-17302ff4dd9-0004-ab9d-931-da694.mp4\",\"properties\":{\"au ... (120436 characters truncated) ... n\":\"\"},{\"begin_time\":407964,\"end_time\":408242,\"text\":\"重\",\"punctuation\":\"\"},{\"begin_time\":408242,\"end_time\":408520,\"text\":\"要\",\"punctuation\":\"。\"}]}]}]}')]"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["session = Session(engine_save)\n", "\n", "# 执行原始SQL查询\n", "sql = text(\"select id, title, url, content from llm_media_ext where status = 1 order by id asc\")\n", "result = session.execute(sql)\n", "\n", "# 获取结果列表\n", "users = result.fetchall()\n", "\n", "session.close()\n", "users"]}, {"cell_type": "code", "execution_count": 26, "id": "1a8a6f2c", "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "for user in users:\n", "    # if user[0] <= 1747: continue\n", "    content = json.loads(user.content)\n", "    str = ''\n", "    for transcript in content['transcripts']:\n", "        str += transcript['text']\n", "    resp = chain.invoke({'input', str})\n", "    json_res = json.loads(resp)\n", "    with Session(engine_save) as session:\n", "        session.add(File(id=user[0], title=user[1], url=user[2]))\n", "        for title in json_res:\n", "            embedding = embeddings.embed_query(title)\n", "            session.add(Data(file_id=user[0], title=title, embedding=embedding))\n", "            session.commit()"]}, {"cell_type": "code", "execution_count": 22, "id": "bf3f321a-f20e-4686-b4dc-52006d0207a7", "metadata": {}, "outputs": [], "source": ["session = Session(engine_save)\n", "\n", "# 执行原始SQL查询\n", "sql = text(\"select id, title from llm_media_relation where embedding is null\")\n", "result = session.execute(sql)\n", "\n", "# 获取结果列表\n", "relations = result.fetchall()\n", "\n", "session.close()"]}, {"cell_type": "code", "execution_count": 23, "id": "40ab170d", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_18936\\4024898208.py:5: DeprecationWarning: \n", "        🚨 You probably want to use `session.exec()` instead of `session.query()`.\n", "\n", "        `session.exec()` is SQLModel's own short version with increased type\n", "        annotations.\n", "\n", "        Or otherwise you might want to use `session.execute()` instead of\n", "        `session.query()`.\n", "        \n", "  session.query(Data).filter(Data.id == relation[0]).update({\"embedding\": embedding})\n"]}], "source": ["for relation in relations:\n", "    with Session(engine_save) as session:\n", "        title = relation[1]\n", "        embedding = embeddings.embed_query(title)\n", "        session.query(Data).filter(Data.id == relation[0]).update({\"embedding\": embedding})\n", "        session.commit()\n", "    "]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 5}
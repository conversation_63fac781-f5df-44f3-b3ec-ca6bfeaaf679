{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from dotenv import load_dotenv\n", "from langchain_openai import ChatOpenAI\n", "import os\n", "\n", "load_dotenv()\n", "\n", "llm = ChatOpenAI(\n", "    api_key=os.getenv('MODAL_API_KEY'),\n", "    base_url=os.getenv('MODAL_URL'),\n", "    model=os.getenv('MODAL_NAME'),\n", "    temperature=1.3,\n", "    max_tokens=9000,\n", ")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from langchain_core.prompts import ChatPromptTemplate\n", "\n", "prompt = ChatPromptTemplate.from_messages([\n", "    ('system', \"\"\"\n", "你是一个翻译专家，负责将中文翻译成日文，尤其擅长小说的翻译工作\n", "请你帮我将以下中文小说翻译成日文小说，风格与日文小说相似\n", "规则：\n", "翻译时要准确传达原文的事实和背景。\n", "策略： 分三步进行翻译工作，并打印每步的结果：\n", "第一步：根据中文内容直译成日文，保持原有格式，不要遗漏任何信息，这一步的结果应该是日文，而不是中文。\n", "第二步：根据第一步直译的结果，指出其中存在的具体问题，要准确描述，不宜笼统的表示，也不需要增加原文不存在的内容或格式，包括不仅限于：\n", "不符合日文表达习惯，明确指出不符合的地方\n", "语句不通顺，指出位置，不需要给出修改意见，意译时修复\n", "晦涩难懂，不易理解，可以尝试给出解释\n", "第三步：根据第一步直译的结果和第二步指出的问题，重新进行意译，保证内容的原意的基础上，使其更易于理解，更符合日文的表达习惯，同时保持原有的格式不变。\n", "\n", "最终的返回格式如下，\"[xxx]\"表示占位符，不要省略：\n", "first:[直译结果]\n", "\n", "second:[直译的具体问题列表]\n", "\n", "third:[意译结果]\n", "\n", "现在，请按照上面的要求从第一行开始翻译以下内容为日文：\n", "    \"\"\"),\n", "    ('user', '{input}')\n", "])"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from langchain_core.output_parsers import StrOutputParser\n", "\n", "output_parser = StrOutputParser()\n", "chain = prompt | llm  "]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["https://www.22biqu.com/biqu41440/21105068.html\n", "answer1结束\n", "answer2结束\n", "answer3结束\n", "https://www.22biqu.com/biqu41440/21105068_2.html\n", "answer1结束\n"]}, {"ename": "IndexError", "evalue": "list index out of range", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mIndexError\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[7], line 48\u001b[0m\n\u001b[0;32m     46\u001b[0m     file\u001b[38;5;241m.\u001b[39mwrite(\u001b[38;5;124m'\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m'\u001b[39m)\n\u001b[0;32m     47\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mopen\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mC:/学习资料/小说/黎明之剑/\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mname\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m-ja-real.txt\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124ma\u001b[39m\u001b[38;5;124m\"\u001b[39m, encoding\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mutf-8\u001b[39m\u001b[38;5;124m'\u001b[39m) \u001b[38;5;28;01mas\u001b[39;00m file:\n\u001b[1;32m---> 48\u001b[0m     file\u001b[38;5;241m.\u001b[39mwrite(\u001b[43manswer2\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcontent\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msplit\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mthird:\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m]\u001b[49m)\n\u001b[0;32m     49\u001b[0m     file\u001b[38;5;241m.\u001b[39mwrite(\u001b[38;5;124m'\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m'\u001b[39m)\n\u001b[0;32m     50\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124manswer2结束\u001b[39m\u001b[38;5;124m'\u001b[39m)\n", "\u001b[1;31mIndexError\u001b[0m: list index out of range"]}], "source": ["import requests\n", "from bs4 import BeautifulSoup\n", "\n", "url = \"https://www.22biqu.com/biqu41440/21105068.html\"\n", "name = '050'\n", "for i in range(2):\n", "    if i == 1:\n", "        url = url.replace('.html', '_2.html')\n", "    print(url) \n", "    response = requests.get(url)\n", "    response.encoding = 'utf-8'  # 根据实际编码调整\n", "\n", "    soup = BeautifulSoup(response.text, 'html.parser')\n", "    content_div = soup.select_one('#content')  # 精确匹配id=content的div\n", "\n", "    if content_div:\n", "        p_tags = content_div.find_all('p')  # 获取所有p标签\n", "        results = [p.get_text(strip=True) for p in p_tags if p.text.strip()]\n", "        #print('\\n\\n'.join(results))\n", "    else:\n", "        print(\"未找到id为content的元素\")\n", "        results = None\n", "\n", "    def split_two(arr):\n", "        mid = len(arr) // 2\n", "        return arr[:mid], arr[mid:]\n", "    def split_three(arr):\n", "       n = len(arr)\n", "       return arr[:n//3], arr[n//3:2*n//3], arr[2*n//3:]\n", "\n", "    if results:\n", "        with open(f\"C:/学习资料/小说/黎明之剑/{name}-zh.txt\", \"a\", encoding='utf-8') as file:\n", "            file.write('\\n\\n'.join(results))\n", "        question1, question2, question3 = split_three(results)\n", "        answer1 = chain.invoke({'input': '\\n\\n'.join(question1)})\n", "        with open(f\"C:/学习资料/小说/黎明之剑/{name}-ja.txt\", \"a\", encoding='utf-8') as file:\n", "            file.write(answer1.content)\n", "            file.write('\\n\\n')\n", "        with open(f\"C:/学习资料/小说/黎明之剑/{name}-ja-real.txt\", \"a\", encoding='utf-8') as file:\n", "            file.write(answer1.content.split('third:', 1)[1])\n", "            file.write('\\n')\n", "        print('answer1结束')\n", "        answer2 = chain.invoke({'input': '\\n\\n'.join(question2)})\n", "        with open(f\"C:/学习资料/小说/黎明之剑/{name}-ja.txt\", \"a\", encoding='utf-8') as file:\n", "            file.write(answer2.content)\n", "            file.write('\\n\\n')\n", "        with open(f\"C:/学习资料/小说/黎明之剑/{name}-ja-real.txt\", \"a\", encoding='utf-8') as file:\n", "            file.write(answer2.content.split('third:', 1)[1])\n", "            file.write('\\n')\n", "        print('answer2结束')\n", "        answer3 = chain.invoke({'input': '\\n\\n'.join(question3)})\n", "        with open(f\"C:/学习资料/小说/黎明之剑/{name}-ja.txt\", \"a\", encoding='utf-8') as file:\n", "            file.write(answer3.content)\n", "            file.write('\\n\\n')\n", "        with open(f\"C:/学习资料/小说/黎明之剑/{name}-ja-real.txt\", \"a\", encoding='utf-8') as file:\n", "            file.write(answer3.content.split('third:', 1)[1])\n", "            file.write('\\n')\n", "        print('answer3结束')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0\n", "1\n"]}], "source": []}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["name = 'lm-063'\n", "with open(f\"C:/学习资料/小说/柠檬汽水糖/翻译修改/{name}.txt\", \"r\", encoding='utf-8') as file:\n", "    content = file.readlines()\n", "    for i in range(len(content)):\n", "        content[i] = content[i].replace(\"陳洛白\", \"黒羽空\")\n", "        content[i] = content[i].replace(\"陈洛白\", \"黒羽空\")\n", "        content[i] = content[i].replace(\"洛哥\", \"黒羽空\")\n", "        content[i] = content[i].replace(\"阿洛\", \"黒羽空\")\n", "        content[i] = content[i].replace(\"周安然\", \"夏目栗奈\")\n", "        content[i] = content[i].replace(\"安然\", \"夏目栗奈\")\n", "        content[i] = content[i].replace(\"然然\", \"夏目栗奈\")\n", "        content[i] = content[i].replace(\"厳星茜\", \"桜木萌絵\")\n", "        content[i] = content[i].replace(\"严星茜\", \"桜木萌絵\")\n", "        content[i] = content[i].replace(\"茜茜\", \"桜木萌絵\")\n", "        content[i] = content[i].replace(\"贺明宇\", \"北川悠真\")\n", "        content[i] = content[i].replace(\"賀明宇\", \"北川悠真\")\n", "        content[i] = content[i].replace(\"祝燃\", \"佐藤悟\")\n", "        content[i] = content[i].replace(\"汤建锐\", \"大森一樹\")\n", "        content[i] = content[i].replace(\"湯建鋭\", \"大森一樹\")\n", "        content[i] = content[i].replace(\"何嘉怡\", \"夏目優\")\n", "        content[i] = content[i].replace(\"周显鸿\", \"夏目秀一\")\n", "        content[i] = content[i].replace(\"宗凯\", \"小林健宗\")\n", "        content[i] = content[i].replace(\"宗凱\", \"小林健宗\")\n", "        content[i] = content[i].replace(\"张舒娴\", \"山田美穂\")\n", "        content[i] = content[i].replace(\"張舒娴\", \"山田美穂\")\n", "        content[i] = content[i].replace(\"張舒嫻\", \"山田美穂\")\n", "        content[i] = content[i].replace(\"娄亦琪\", \"佐藤美紀\")\n", "        content[i] = content[i].replace(\"婁亦琪\", \"佐藤美紀\")\n", "        content[i] = content[i].replace(\"盛晓雯\", \"星野絵里香\")\n", "        content[i] = content[i].replace(\"董辰\", \"伊藤明\")\n", "        content[i] = content[i].replace(\"殷宜真\", \"宮崎莉真\")\n", "        content[i] = content[i].replace(\"胡琨\", \"田中翔\")\n", "        content[i] = content[i].replace(\"黄書傑\", \"中村陽介\")\n", "        content[i] = content[i].replace(\"黄书杰\", \"中村陽介\")\n", "        content[i] = content[i].replace(\"包坤\", \"森田和也\")\n", "        content[i] = content[i].replace(\"邵子林\", \"野村太郎\")\n", "        content[i] = content[i].replace(\"岑瑜\", \"岩崎咲羽\")\n", "        content[i] = content[i].replace(\"徐洪亮\", \"山下亮平\")\n", "        content[i] = content[i].replace(\"于欣月\", \"中村月穂\")\n", "        content[i] = content[i].replace(\"柏灵云\", \"佐藤麗\")\n", "        content[i] = content[i].replace(\"柏霊雲\", \"佐藤麗\")\n", "        content[i] = content[i].replace(\"谢静怡\", \"松本静\")\n", "        content[i] = content[i].replace(\"謝静谊\", \"松本静\")\n", "        content[i] = content[i].replace(\"謝静誼\", \"松本静\")\n", "        content[i] = content[i].replace(\"謝学长\", \"村上学长\")\n", "        content[i] = content[i].replace(\"杜亦舟\", \"土屋亦斗\")\n", "        content[i] = content[i].replace(\"俞冰沁\", \"鈴木凛音\")\n", "        content[i] = content[i].replace(\"俞氷沁\", \"鈴木凛音\")\n", "        content[i] = content[i].replace(\"兪氷沁\", \"鈴木凛音\")\n", "        content[i] = content[i].replace(\"元松\", \"松本拓真\")\n", "        content[i] = content[i].replace(\"聶子蓁\", \"高橋明里子\")\n", "        content[i] = content[i].replace(\"盛暁雯\", \"星野絵里香\")\n", "        content[i] = content[i].replace(\"周清随\", \"小林清太\")\n", "        content[i] = content[i].replace(\"巩永亮\", \"白石永亮\")\n", "        content[i] = content[i].replace(\"伏晓烽\", \"黑沼烽\")\n", "with open(f\"C:/学习资料/小说/柠檬汽水糖/翻译修改/{name}-final.txt\", \"w\", encoding='utf-8') as file:\n", "    file.write(''.join(content))\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 2}
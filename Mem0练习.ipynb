{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple\n", "Collecting mem0ai\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/5c/b5/6c276bb74f445ffddb28fb1f3b745f0f721eeeb43ef481bd7bff4842da39/mem0ai-0.1.81-py3-none-any.whl (126 kB)\n", "Requirement already satisfied: azure-search-documents<12.0.0,>=11.5.0 in c:\\software\\python\\lib\\site-packages (from mem0ai) (11.5.2)\n", "Requirement already satisfied: openai<2.0.0,>=1.33.0 in c:\\software\\python\\lib\\site-packages (from mem0ai) (1.61.1)\n", "Requirement already satisfied: posthog<4.0.0,>=3.5.0 in c:\\software\\python\\lib\\site-packages (from mem0ai) (3.14.1)\n", "Collecting psycopg2-binary<3.0.0,>=2.9.10 (from mem0ai)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/92/29/06261ea000e2dc1e22907dbbc483a1093665509ea586b29b8986a0e56733/psycopg2_binary-2.9.10-cp312-cp312-win_amd64.whl (1.2 MB)\n", "     ---------------------------------------- 0.0/1.2 MB ? eta -:--:--\n", "     --------- ------------------------------ 0.3/1.2 MB ? eta -:--:--\n", "     ---------------------------------------- 1.2/1.2 MB 3.2 MB/s eta 0:00:00\n", "Requirement already satisfied: pydantic<3.0.0,>=2.7.3 in c:\\software\\python\\lib\\site-packages (from mem0ai) (2.10.6)\n", "Requirement already satisfied: pytz<2025.0,>=2024.1 in c:\\software\\python\\lib\\site-packages (from mem0ai) (2024.2)\n", "Requirement already satisfied: qdrant-client<2.0.0,>=1.9.1 in c:\\software\\python\\lib\\site-packages (from mem0ai) (1.12.2)\n", "Requirement already satisfied: sqlalchemy<3.0.0,>=2.0.31 in c:\\software\\python\\lib\\site-packages (from mem0ai) (2.0.32)\n", "Requirement already satisfied: azure-core>=1.28.0 in c:\\software\\python\\lib\\site-packages (from azure-search-documents<12.0.0,>=11.5.0->mem0ai) (1.32.0)\n", "Requirement already satisfied: azure-common>=1.1 in c:\\software\\python\\lib\\site-packages (from azure-search-documents<12.0.0,>=11.5.0->mem0ai) (1.1.28)\n", "Requirement already satisfied: isodate>=0.6.0 in c:\\software\\python\\lib\\site-packages (from azure-search-documents<12.0.0,>=11.5.0->mem0ai) (0.7.2)\n", "Requirement already satisfied: typing-extensions>=4.6.0 in c:\\software\\python\\lib\\site-packages (from azure-search-documents<12.0.0,>=11.5.0->mem0ai) (4.12.2)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in c:\\software\\python\\lib\\site-packages (from openai<2.0.0,>=1.33.0->mem0ai) (4.4.0)\n", "Requirement already satisfied: distro<2,>=1.7.0 in c:\\software\\python\\lib\\site-packages (from openai<2.0.0,>=1.33.0->mem0ai) (1.9.0)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in c:\\software\\python\\lib\\site-packages (from openai<2.0.0,>=1.33.0->mem0ai) (0.28.1)\n", "Requirement already satisfied: jiter<1,>=0.4.0 in c:\\software\\python\\lib\\site-packages (from openai<2.0.0,>=1.33.0->mem0ai) (0.8.2)\n", "Requirement already satisfied: sniffio in c:\\software\\python\\lib\\site-packages (from openai<2.0.0,>=1.33.0->mem0ai) (1.3.1)\n", "Requirement already satisfied: tqdm>4 in c:\\software\\python\\lib\\site-packages (from openai<2.0.0,>=1.33.0->mem0ai) (4.67.1)\n", "Requirement already satisfied: requests<3.0,>=2.7 in c:\\software\\python\\lib\\site-packages (from posthog<4.0.0,>=3.5.0->mem0ai) (2.32.3)\n", "Requirement already satisfied: six>=1.5 in c:\\software\\python\\lib\\site-packages (from posthog<4.0.0,>=3.5.0->mem0ai) (1.16.0)\n", "Requirement already satisfied: monotonic>=1.5 in c:\\software\\python\\lib\\site-packages (from posthog<4.0.0,>=3.5.0->mem0ai) (1.6)\n", "Requirement already satisfied: backoff>=1.10.0 in c:\\software\\python\\lib\\site-packages (from posthog<4.0.0,>=3.5.0->mem0ai) (2.2.1)\n", "Requirement already satisfied: python-dateutil>2.1 in c:\\software\\python\\lib\\site-packages (from posthog<4.0.0,>=3.5.0->mem0ai) (2.9.0.post0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in c:\\software\\python\\lib\\site-packages (from pydantic<3.0.0,>=2.7.3->mem0ai) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.27.2 in c:\\software\\python\\lib\\site-packages (from pydantic<3.0.0,>=2.7.3->mem0ai) (2.27.2)\n", "Requirement already satisfied: grpcio>=1.41.0 in c:\\software\\python\\lib\\site-packages (from qdrant-client<2.0.0,>=1.9.1->mem0ai) (1.67.1)\n", "Requirement already satisfied: grpcio-tools>=1.41.0 in c:\\software\\python\\lib\\site-packages (from qdrant-client<2.0.0,>=1.9.1->mem0ai) (1.62.3)\n", "Requirement already satisfied: numpy>=1.26 in c:\\software\\python\\lib\\site-packages (from qdrant-client<2.0.0,>=1.9.1->mem0ai) (1.26.4)\n", "Requirement already satisfied: portalocker<3.0.0,>=2.7.0 in c:\\software\\python\\lib\\site-packages (from qdrant-client<2.0.0,>=1.9.1->mem0ai) (2.10.1)\n", "Requirement already satisfied: urllib3<3,>=1.26.14 in c:\\software\\python\\lib\\site-packages (from qdrant-client<2.0.0,>=1.9.1->mem0ai) (1.26.20)\n", "Requirement already satisfied: greenlet!=0.4.17 in c:\\software\\python\\lib\\site-packages (from sqlalchemy<3.0.0,>=2.0.31->mem0ai) (3.1.1)\n", "Requirement already satisfied: idna>=2.8 in c:\\software\\python\\lib\\site-packages (from anyio<5,>=3.5.0->openai<2.0.0,>=1.33.0->mem0ai) (3.10)\n", "Requirement already satisfied: protobuf<5.0dev,>=4.21.6 in c:\\software\\python\\lib\\site-packages (from grpcio-tools>=1.41.0->qdrant-client<2.0.0,>=1.9.1->mem0ai) (4.25.6)\n", "Requirement already satisfied: setuptools in c:\\software\\python\\lib\\site-packages (from grpcio-tools>=1.41.0->qdrant-client<2.0.0,>=1.9.1->mem0ai) (72.1.0)\n", "Requirement already satisfied: certifi in c:\\software\\python\\lib\\site-packages (from httpx<1,>=0.23.0->openai<2.0.0,>=1.33.0->mem0ai) (2025.1.31)\n", "Requirement already satisfied: httpcore==1.* in c:\\software\\python\\lib\\site-packages (from httpx<1,>=0.23.0->openai<2.0.0,>=1.33.0->mem0ai) (1.0.5)\n", "Requirement already satisfied: h11<0.15,>=0.13 in c:\\software\\python\\lib\\site-packages (from httpcore==1.*->httpx<1,>=0.23.0->openai<2.0.0,>=1.33.0->mem0ai) (0.14.0)\n", "Requirement already satisfied: h2<5,>=3 in c:\\software\\python\\lib\\site-packages (from httpx[http2]>=0.20.0->qdrant-client<2.0.0,>=1.9.1->mem0ai) (4.2.0)\n", "Requirement already satisfied: pywin32>=226 in c:\\software\\python\\lib\\site-packages (from portalocker<3.0.0,>=2.7.0->qdrant-client<2.0.0,>=1.9.1->mem0ai) (306)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in c:\\software\\python\\lib\\site-packages (from requests<3.0,>=2.7->posthog<4.0.0,>=3.5.0->mem0ai) (3.4.1)\n", "Requirement already satisfied: colorama in c:\\software\\python\\lib\\site-packages (from tqdm>4->openai<2.0.0,>=1.33.0->mem0ai) (0.4.6)\n", "Requirement already satisfied: hyperframe<7,>=6.1 in c:\\software\\python\\lib\\site-packages (from h2<5,>=3->httpx[http2]>=0.20.0->qdrant-client<2.0.0,>=1.9.1->mem0ai) (6.1.0)\n", "Requirement already satisfied: hpack<5,>=4.1 in c:\\software\\python\\lib\\site-packages (from h2<5,>=3->httpx[http2]>=0.20.0->qdrant-client<2.0.0,>=1.9.1->mem0ai) (4.1.0)\n", "Installing collected packages: psycopg2-binary, mem0ai\n", "  Attempting uninstall: psycopg2-binary\n", "    Found existing installation: psycopg2-binary 2.9.9\n", "    Uninstalling psycopg2-binary-2.9.9:\n", "      Successfully uninstalled psycopg2-binary-2.9.9\n", "Successfully installed mem0ai-0.1.81 psycopg2-binary-2.9.10\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "[notice] A new release of pip is available: 24.2 -> 25.0.1\n", "[notice] To update, run: python.exe -m pip install --upgrade pip\n"]}], "source": []}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["config = {\n", "    \"vector_store\": {\n", "        \"provider\": \"pgvector\",\n", "        \"config\": {\n", "            \"host\": \"pgm-bp1j698rf1n2nq7rdo.rwlb.rds.aliyuncs.com\",\n", "            \"port\": 5432,\n", "            \"dbname\": \"fuying_vector\",\n", "            \"collection_name\": \"public\",\n", "            \"user\": \"fuying_pg\",\n", "            \"password\": \"#Fuying#2021#\",\n", "        }\n", "    },\n", "    \"llm\": {\n", "        \"provider\": \"openai\",\n", "        \"config\": {\n", "            \"openai_base_url\": \"https://dashscope.aliyuncs.com/compatible-mode/v1\",\n", "            \"api_key\": \"sk-5d2090a0ac394b218376b67cb91123f5\",\n", "            \"model\": \"deepseek-r1\"\n", "        }\n", "    },\n", "    \"embedder\": {\n", "        \"provider\": \"openai\",\n", "        \"config\": {\n", "            \"openai_base_url\": \"https://dashscope.aliyuncs.com/compatible-mode/v1\",\n", "            \"api_key\": \"sk-5d2090a0ac394b218376b67cb91123f5\",\n", "            \"model\": \"text-embedding-v3\"\n", "        }\n", "    },\n", "    \"history_db_path\": \"C:/workspace/ai-open/history.db\",\n", "    \"version\": \"v1.1\",\n", "}"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["from mem0 import Memory\n", "\n", "mem = Memory.from_config(config)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"ename": "BadRequestError", "evalue": "Error code: 400 - {'error': {'code': 'InvalidParameter', 'param': None, 'message': '<400> InternalError.Algo.InvalidParameter: Value error, dimension for embedding v3 is invalid, its value shold be in [64, 128, 256, 512, 768, 1024]: parameters.dimension', 'type': 'InvalidParameter'}, 'id': 'f975eb60-09f6-9991-8e55-85035ecaaa9f', 'request_id': 'f975eb60-09f6-9991-8e55-85035ecaaa9f'}", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mBadRequestError\u001b[0m                           <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[7], line 9\u001b[0m\n\u001b[0;32m      1\u001b[0m messages \u001b[38;5;241m=\u001b[39m [\n\u001b[0;32m      2\u001b[0m     {\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mrole\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124muser\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcontent\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mI\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mm planning to watch a movie tonight. Any recommendations?\u001b[39m\u001b[38;5;124m\"\u001b[39m},\n\u001b[0;32m      3\u001b[0m     {\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mrole\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124massistant\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcontent\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mHow about a thriller movies? They can be quite engaging.\u001b[39m\u001b[38;5;124m\"\u001b[39m},\n\u001b[0;32m      4\u001b[0m     {\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mrole\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124muser\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcontent\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mI\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mm not a big fan of thriller movies but I love sci-fi movies.\u001b[39m\u001b[38;5;124m\"\u001b[39m},\n\u001b[0;32m      5\u001b[0m     {\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mrole\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124massistant\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcontent\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mGot it! I\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mll avoid thriller recommendations and suggest sci-fi movies in the future.\u001b[39m\u001b[38;5;124m\"\u001b[39m}\n\u001b[0;32m      6\u001b[0m ]\n\u001b[0;32m      8\u001b[0m \u001b[38;5;66;03m# Store inferred memories (default behavior)\u001b[39;00m\n\u001b[1;32m----> 9\u001b[0m result \u001b[38;5;241m=\u001b[39m \u001b[43mmem\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43madd\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmessages\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43muser_id\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43malice\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmetadata\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m{\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mcategory\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mmovie_recommendations\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m}\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mc:\\software\\python\\Lib\\site-packages\\mem0\\memory\\main.py:158\u001b[0m, in \u001b[0;36mMemory.add\u001b[1;34m(self, messages, user_id, agent_id, run_id, metadata, filters, infer, memory_type, prompt, llm)\u001b[0m\n\u001b[0;32m    154\u001b[0m     future2 \u001b[38;5;241m=\u001b[39m executor\u001b[38;5;241m.\u001b[39msubmit(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_add_to_graph, messages, filters)\n\u001b[0;32m    156\u001b[0m     concurrent\u001b[38;5;241m.\u001b[39mfutures\u001b[38;5;241m.\u001b[39mwait([future1, future2])\n\u001b[1;32m--> 158\u001b[0m     vector_store_result \u001b[38;5;241m=\u001b[39m \u001b[43mfuture1\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mresult\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    159\u001b[0m     graph_result \u001b[38;5;241m=\u001b[39m future2\u001b[38;5;241m.\u001b[39mresult()\n\u001b[0;32m    161\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mapi_version \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mv1.0\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n", "File \u001b[1;32mc:\\software\\python\\Lib\\concurrent\\futures\\_base.py:449\u001b[0m, in \u001b[0;36mFuture.result\u001b[1;34m(self, timeout)\u001b[0m\n\u001b[0;32m    447\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m CancelledError()\n\u001b[0;32m    448\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_state \u001b[38;5;241m==\u001b[39m FINISHED:\n\u001b[1;32m--> 449\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m__get_result\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    451\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_condition\u001b[38;5;241m.\u001b[39mwait(timeout)\n\u001b[0;32m    453\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_state \u001b[38;5;129;01min\u001b[39;00m [CANCELLED, CANCELLED_AND_NOTIFIED]:\n", "File \u001b[1;32mc:\\software\\python\\Lib\\concurrent\\futures\\_base.py:401\u001b[0m, in \u001b[0;36mFuture.__get_result\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    399\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_exception:\n\u001b[0;32m    400\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m--> 401\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_exception\n\u001b[0;32m    402\u001b[0m     \u001b[38;5;28;01mfinally\u001b[39;00m:\n\u001b[0;32m    403\u001b[0m         \u001b[38;5;66;03m# Break a reference cycle with the exception in self._exception\u001b[39;00m\n\u001b[0;32m    404\u001b[0m         \u001b[38;5;28mself\u001b[39m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n", "File \u001b[1;32mc:\\software\\python\\Lib\\concurrent\\futures\\thread.py:58\u001b[0m, in \u001b[0;36m_WorkItem.run\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m     55\u001b[0m     \u001b[38;5;28;01<PERSON><PERSON><PERSON>\u001b[39;00m\n\u001b[0;32m     57\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m---> 58\u001b[0m     result \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfn\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     59\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mBaseException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m exc:\n\u001b[0;32m     60\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mfuture\u001b[38;5;241m.\u001b[39mset_exception(exc)\n", "File \u001b[1;32mc:\\software\\python\\Lib\\site-packages\\mem0\\memory\\main.py:215\u001b[0m, in \u001b[0;36mMemory._add_to_vector_store\u001b[1;34m(self, messages, metadata, filters, infer)\u001b[0m\n\u001b[0;32m    213\u001b[0m new_message_embeddings \u001b[38;5;241m=\u001b[39m {}\n\u001b[0;32m    214\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m new_mem \u001b[38;5;129;01min\u001b[39;00m new_retrieved_facts:\n\u001b[1;32m--> 215\u001b[0m     messages_embeddings \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43membedding_model\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43membed\u001b[49m\u001b[43m(\u001b[49m\u001b[43mnew_mem\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43madd\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[0;32m    216\u001b[0m     new_message_embeddings[new_mem] \u001b[38;5;241m=\u001b[39m messages_embeddings\n\u001b[0;32m    217\u001b[0m     existing_memories \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mvector_store\u001b[38;5;241m.\u001b[39msearch(\n\u001b[0;32m    218\u001b[0m         query\u001b[38;5;241m=\u001b[39mnew_mem,\n\u001b[0;32m    219\u001b[0m         vectors\u001b[38;5;241m=\u001b[39mmessages_embeddings,\n\u001b[0;32m    220\u001b[0m         limit\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m5\u001b[39m,\n\u001b[0;32m    221\u001b[0m         filters\u001b[38;5;241m=\u001b[39mfilters,\n\u001b[0;32m    222\u001b[0m     )\n", "File \u001b[1;32mc:\\software\\python\\Lib\\site-packages\\mem0\\embeddings\\openai.py:46\u001b[0m, in \u001b[0;36mOpenAIEmbedding.embed\u001b[1;34m(self, text, memory_action)\u001b[0m\n\u001b[0;32m     35\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[0;32m     36\u001b[0m \u001b[38;5;124;03mGet the embedding for the given text using OpenAI.\u001b[39;00m\n\u001b[0;32m     37\u001b[0m \n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m     42\u001b[0m \u001b[38;5;124;03m    list: The embedding vector.\u001b[39;00m\n\u001b[0;32m     43\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[0;32m     44\u001b[0m text \u001b[38;5;241m=\u001b[39m text\u001b[38;5;241m.\u001b[39mreplace(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m \u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m     45\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m (\n\u001b[1;32m---> 46\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mclient\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43membeddings\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcreate\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43minput\u001b[39;49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m[\u001b[49m\u001b[43mtext\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmodel\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mconfig\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmodel\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdimensions\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mconfig\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43membedding_dims\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     47\u001b[0m     \u001b[38;5;241m.\u001b[39mdata[\u001b[38;5;241m0\u001b[39m]\n\u001b[0;32m     48\u001b[0m     \u001b[38;5;241m.\u001b[39membedding\n\u001b[0;32m     49\u001b[0m )\n", "File \u001b[1;32mc:\\software\\python\\Lib\\site-packages\\openai\\resources\\embeddings.py:125\u001b[0m, in \u001b[0;36mEmbeddings.create\u001b[1;34m(self, input, model, dimensions, encoding_format, user, extra_headers, extra_query, extra_body, timeout)\u001b[0m\n\u001b[0;32m    119\u001b[0m         embedding\u001b[38;5;241m.\u001b[39membedding \u001b[38;5;241m=\u001b[39m np\u001b[38;5;241m.\u001b[39mfrombuffer(  \u001b[38;5;66;03m# type: ignore[no-untyped-call]\u001b[39;00m\n\u001b[0;32m    120\u001b[0m             base64\u001b[38;5;241m.\u001b[39mb64decode(data), dtype\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mfloat32\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    121\u001b[0m         )\u001b[38;5;241m.\u001b[39mtolist()\n\u001b[0;32m    123\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m obj\n\u001b[1;32m--> 125\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_post\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    126\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m/embeddings\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[0;32m    127\u001b[0m \u001b[43m    \u001b[49m\u001b[43mbody\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmaybe_transform\u001b[49m\u001b[43m(\u001b[49m\u001b[43mparams\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43membedding_create_params\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mEmbeddingCreateParams\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    128\u001b[0m \u001b[43m    \u001b[49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmake_request_options\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    129\u001b[0m \u001b[43m        \u001b[49m\u001b[43mextra_headers\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mextra_headers\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    130\u001b[0m \u001b[43m        \u001b[49m\u001b[43mextra_query\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mextra_query\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    131\u001b[0m \u001b[43m        \u001b[49m\u001b[43mextra_body\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mextra_body\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    132\u001b[0m \u001b[43m        \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    133\u001b[0m \u001b[43m        \u001b[49m\u001b[43mpost_parser\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mparser\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    134\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    135\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcast_to\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mCreateEmbeddingResponse\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    136\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mc:\\software\\python\\Lib\\site-packages\\openai\\_base_client.py:1283\u001b[0m, in \u001b[0;36mSyncAPIClient.post\u001b[1;34m(self, path, cast_to, body, options, files, stream, stream_cls)\u001b[0m\n\u001b[0;32m   1269\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mpost\u001b[39m(\n\u001b[0;32m   1270\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[0;32m   1271\u001b[0m     path: \u001b[38;5;28mstr\u001b[39m,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m   1278\u001b[0m     stream_cls: \u001b[38;5;28mtype\u001b[39m[_StreamT] \u001b[38;5;241m|\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[0;32m   1279\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m ResponseT \u001b[38;5;241m|\u001b[39m _StreamT:\n\u001b[0;32m   1280\u001b[0m     opts \u001b[38;5;241m=\u001b[39m FinalRequestOptions\u001b[38;5;241m.\u001b[39mconstruct(\n\u001b[0;32m   1281\u001b[0m         method\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mpost\u001b[39m\u001b[38;5;124m\"\u001b[39m, url\u001b[38;5;241m=\u001b[39mpath, json_data\u001b[38;5;241m=\u001b[39mbody, files\u001b[38;5;241m=\u001b[39mto_httpx_files(files), \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39moptions\n\u001b[0;32m   1282\u001b[0m     )\n\u001b[1;32m-> 1283\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m cast(ResponseT, \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcast_to\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mopts\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstream\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstream_cls\u001b[49m\u001b[43m)\u001b[49m)\n", "File \u001b[1;32mc:\\software\\python\\Lib\\site-packages\\openai\\_base_client.py:960\u001b[0m, in \u001b[0;36mSyncAPIClient.request\u001b[1;34m(self, cast_to, options, remaining_retries, stream, stream_cls)\u001b[0m\n\u001b[0;32m    957\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m    958\u001b[0m     retries_taken \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m0\u001b[39m\n\u001b[1;32m--> 960\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_request\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    961\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcast_to\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcast_to\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    962\u001b[0m \u001b[43m    \u001b[49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    963\u001b[0m \u001b[43m    \u001b[49m\u001b[43mstream\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstream\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    964\u001b[0m \u001b[43m    \u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstream_cls\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    965\u001b[0m \u001b[43m    \u001b[49m\u001b[43mretries_taken\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mretries_taken\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    966\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mc:\\software\\python\\Lib\\site-packages\\openai\\_base_client.py:1064\u001b[0m, in \u001b[0;36mSyncAPIClient._request\u001b[1;34m(self, cast_to, options, retries_taken, stream, stream_cls)\u001b[0m\n\u001b[0;32m   1061\u001b[0m         err\u001b[38;5;241m.\u001b[39mresponse\u001b[38;5;241m.\u001b[39mread()\n\u001b[0;32m   1063\u001b[0m     log\u001b[38;5;241m.\u001b[39mdebug(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mRe-raising status error\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m-> 1064\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_make_status_error_from_response(err\u001b[38;5;241m.\u001b[39mresponse) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[0;32m   1066\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_process_response(\n\u001b[0;32m   1067\u001b[0m     cast_to\u001b[38;5;241m=\u001b[39mcast_to,\n\u001b[0;32m   1068\u001b[0m     options\u001b[38;5;241m=\u001b[39moptions,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m   1072\u001b[0m     retries_taken\u001b[38;5;241m=\u001b[39mretries_taken,\n\u001b[0;32m   1073\u001b[0m )\n", "\u001b[1;31mBadRequestError\u001b[0m: Error code: 400 - {'error': {'code': 'InvalidParameter', 'param': None, 'message': '<400> InternalError.Algo.InvalidParameter: Value error, dimension for embedding v3 is invalid, its value shold be in [64, 128, 256, 512, 768, 1024]: parameters.dimension', 'type': 'InvalidParameter'}, 'id': 'f975eb60-09f6-9991-8e55-85035ecaaa9f', 'request_id': 'f975eb60-09f6-9991-8e55-85035ecaaa9f'}"]}], "source": ["messages = [\n", "    {\"role\": \"user\", \"content\": \"I'm planning to watch a movie tonight. Any recommendations?\"},\n", "    {\"role\": \"assistant\", \"content\": \"How about a thriller movies? They can be quite engaging.\"},\n", "    {\"role\": \"user\", \"content\": \"I'm not a big fan of thriller movies but I love sci-fi movies.\"},\n", "    {\"role\": \"assistant\", \"content\": \"Got it! I'll avoid thriller recommendations and suggest sci-fi movies in the future.\"}\n", "]\n", "\n", "# Store inferred memories (default behavior)\n", "result = mem.add(messages, user_id=\"alice\", metadata={\"category\": \"movie_recommendations\"})\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["<frozen importlib._bootstrap>:488: DeprecationWarning: Type google._upb._message.MessageMapContainer uses PyType_Spec with a metaclass that has custom tp_new. This is deprecated and will no longer be allowed in Python 3.14.\n", "<frozen importlib._bootstrap>:488: DeprecationWarning: Type google._upb._message.ScalarMapContainer uses PyType_Spec with a metaclass that has custom tp_new. This is deprecated and will no longer be allowed in Python 3.14.\n", "c:\\software\\python\\Lib\\site-packages\\mem0\\client\\main.py:32: DeprecationWarning: Using default output format 'v1.0' is deprecated and will be removed in version 0.1.70. Please use output_format='v1.1' for enhanced memory details. Check out the docs for more information: https://docs.mem0.ai/platform/quickstart#4-1-create-memories\n", "  return func(*args, **kwargs)\n"]}, {"data": {"text/plain": ["[{'id': 'a0b815f7-178c-4cca-8abd-8f19e82ab0f5',\n", "  'data': {'memory': 'Name is <PERSON>'},\n", "  'event': 'ADD'},\n", " {'id': '5bf05ac3-fbad-47ff-9686-9c3276522360',\n", "  'data': {'memory': 'Is a vegetarian'},\n", "  'event': 'ADD'},\n", " {'id': '3aa34729-26d7-4532-a285-cc2c12ad77f2',\n", "  'data': {'memory': 'Is allergic to nuts'},\n", "  'event': 'ADD'}]"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from mem0 import MemoryClient\n", "client = MemoryClient(api_key=\"m0-Dp71bb82Y2Vb6xjZgLekU7r1CY7le3DNDuQlmJ6r\")\n", "messages = [\n", "    {\"role\": \"user\", \"content\": \"<PERSON>, I'm <PERSON>. I'm a vegetarian and I'm allergic to nuts.\"},\n", "    {\"role\": \"assistant\", \"content\": \"Hello <PERSON>! I've noted that you're a vegetarian and have a nut allergy. I'll keep this in mind for any food-related recommendations or discussions.\"}\n", "]\n", "client.add(messages, user_id=\"alex\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["query = \"What can I cook for dinner tonight?\"\n", "res = client.search(query, user_id=\"alex\")\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'id': '5bf05ac3-fbad-47ff-9686-9c3276522360',\n", "  'memory': 'Is a vegetarian',\n", "  'user_id': 'alex',\n", "  'metadata': None,\n", "  'categories': ['user_preferences', 'food'],\n", "  'immutable': <PERSON><PERSON><PERSON>,\n", "  'created_at': '2025-04-03T00:19:16.798071-07:00',\n", "  'updated_at': '2025-04-03T00:19:16.798081-07:00',\n", "  'extraction_type': 'extracted_details',\n", "  'expiration_date': None,\n", "  'score': 0.30164014110804493}]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["res"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 2}
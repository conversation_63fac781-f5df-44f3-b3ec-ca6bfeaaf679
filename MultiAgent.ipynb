{"cells": [{"cell_type": "code", "execution_count": 3, "id": "3320e496-645a-4a47-9f06-ab85577aa061", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "import pandas as pd\n", "import tiktoken\n", "\n", "from graphrag.query.context_builder.entity_extraction import EntityVectorStoreKey\n", "from graphrag.query.indexer_adapters import (\n", "    read_indexer_covariates,\n", "    read_indexer_entities,\n", "    read_indexer_relationships,\n", "    read_indexer_reports,\n", "    read_indexer_text_units,\n", ")\n", "from graphrag.query.llm.oai.chat_openai import ChatOpenAI\n", "from graphrag.query.llm.oai.embedding import OpenAIEmbedding\n", "from graphrag.query.llm.oai.typing import OpenaiApiType\n", "from graphrag.query.question_gen.local_gen import LocalQuestionGen\n", "from graphrag.query.structured_search.local_search.mixed_context import (\n", "    LocalSearchMixedContext,\n", ")\n", "from graphrag.query.structured_search.local_search.search import LocalSearch\n", "from graphrag.vector_stores.lancedb import LanceDBVectorStore\n", "    \n", "INPUT_DIR = \"C:/workspace/graph-rag/test1/output\"\n", "LANCEDB_URI = f\"{INPUT_DIR}/lancedb\"\n", "\n", "COMMUNITY_REPORT_TABLE = \"create_final_community_reports\"\n", "ENTITY_TABLE = \"create_final_nodes\"\n", "ENTITY_EMBEDDING_TABLE = \"create_final_entities\"\n", "COMMUNITY_TABLE = \"create_final_communities\"\n", "RELATIONSHIP_TABLE = \"create_final_relationships\"\n", "#COVARIATE_TABLE = \"create_final_covariates\"\n", "TEXT_UNIT_TABLE = \"create_final_text_units\"\n", "COMMUNITY_LEVEL = 2\n", "\n", "class MyRAG:\n", "    def __init__(self):\n", "        description_embedding_store = LanceDBVectorStore(\n", "            collection_name=\"default-entity-description\",\n", "        )\n", "        description_embedding_store.connect(db_uri=LANCEDB_URI)\n", "\n", "        entity_df = pd.read_parquet(f\"{INPUT_DIR}/{ENTITY_TABLE}.parquet\")\n", "        entity_embedding_df = pd.read_parquet(f\"{INPUT_DIR}/{ENTITY_EMBEDDING_TABLE}.parquet\")\n", "        entities = read_indexer_entities(entity_df, entity_embedding_df, COMMUNITY_LEVEL)\n", "\n", "        relationship_df = pd.read_parquet(f\"{INPUT_DIR}/{RELATIONSHIP_TABLE}.parquet\")\n", "        relationships = read_indexer_relationships(relationship_df)\n", "\n", "        report_df = pd.read_parquet(f\"{INPUT_DIR}/{COMMUNITY_REPORT_TABLE}.parquet\")\n", "        reports = read_indexer_reports(report_df, entity_df, COMMUNITY_LEVEL)\n", "\n", "        text_unit_df = pd.read_parquet(f\"{INPUT_DIR}/{TEXT_UNIT_TABLE}.parquet\")\n", "        text_units = read_indexer_text_units(text_unit_df)\n", "\n", "        embedding_model = \"text-embedding-v3\"\n", "        text_embedder = OpenAIEmbedding(\n", "            api_key=os.getenv('DASHSCOPE_API_KEY'),\n", "            api_base=os.getenv('DASHSCOPE_URL'),\n", "            api_type=OpenaiApiType.OpenAI,\n", "            model=embedding_model,\n", "            deployment_name=embedding_model,\n", "            max_retries=20,\n", "        )\n", "\n", "        token_encoder = tiktoken.get_encoding(\"cl100k_base\")\n", "\n", "        self.context_builder = LocalSearchMixedContext(\n", "            community_reports=reports,\n", "            text_units=text_units,\n", "            entities=entities,\n", "            relationships=relationships,\n", "            # if you did not run covariates during indexing, set this to None\n", "            #covariates=covariates,\n", "            entity_text_embeddings=description_embedding_store,\n", "            embedding_vectorstore_key=EntityVectorStoreKey.ID,  # if the vectorstore uses entity title as ids, set this to EntityVectorStoreKey.TITLE\n", "            text_embedder=text_embedder,\n", "            token_encoder=token_encoder,\n", "        )\n", "\n", "    def query(self, query: str): \n", "        context = self.context_builder.build_context(query)\n", "        return self.__parse_res(context.context_chunks)\n", "    \n", "    def __parse_res(self, res: str):\n", "        sections = res.split('\\n\\n\\n')  \n", "        source_content = \"\"\n", "        for section in sections:\n", "            if section.strip().startswith(\"-----Sources-----\"): \n", "                source_content = section\n", "                break\n", "        \n", "        content_arr = source_content.split('|')\n", "        content = content_arr[-1:]\n", "        return content[0]"]}, {"cell_type": "code", "execution_count": 5, "id": "640d0159-1d2e-4603-99a2-f005d217ef2e", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "from dotenv import load_dotenv, find_dotenv\n", "\n", "load_dotenv()"]}, {"cell_type": "code", "execution_count": 6, "id": "12c0e3e9-dfa8-4f41-b320-7b4590132623", "metadata": {}, "outputs": [], "source": ["from langchain.tools import BaseTool, StructuredTool, tool\n", "\n", "rag = MyRAG()\n", "\n", "@tool\n", "def introduce(text: str):\n", "    '''用于介绍公司和课程的基本信息,当询问扶鹰公司和520、二阶等课程的介绍时应该使用该工具'''\n", "    return rag.query(text)"]}, {"cell_type": "code", "execution_count": 7, "id": "74e95068-2d98-4652-bf01-d61393a56a05", "metadata": {}, "outputs": [], "source": ["from langchain_openai import ChatOpenAI\n", "import os\n", "\n", "llm = ChatOpenAI(\n", "    api_key=os.getenv('MODAL_API_KEY'),\n", "    base_url=os.getenv('MODAL_URL'),\n", "    model=os.getenv('MODAL_NAME'),\n", "    # other params...\n", ")"]}, {"cell_type": "code", "execution_count": 8, "id": "bebb81ff-f6c7-42e3-adad-27b25756dee7", "metadata": {}, "outputs": [{"data": {"text/plain": ["'deepseek-v3'"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["os.getenv('MODAL_NAME')"]}, {"cell_type": "code", "execution_count": 50, "id": "74f8fe16-1273-4321-a446-daf6a780df2b", "metadata": {}, "outputs": [], "source": ["# from langchain_community.chat_models.tongyi import ChatTongyi\n", "\n", "# llm = Chat<PERSON><PERSON>yi(api_key=os.getenv('MODAL_API_KEY'),model_name=os.getenv('MODAL_NAME'),temperature=0.7)\n", "# llm_with_tools = llm.bind_tools(tools)\n", "# llm_with_tools.invoke(\"扶鹰是做什么的\")"]}, {"cell_type": "code", "execution_count": 9, "id": "2a1db20d-6c20-4682-bcd6-51e0e108bc44", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\software\\python\\Lib\\site-packages\\langsmith\\client.py:241: LangSmithMissingAPIKeyWarning: API key must be provided when using hosted LangSmith API\n", "  warnings.warn(\n"]}], "source": ["from langchain import hub\n", "from langchain.agents import create_tool_calling_agent, AgentExecutor\n", "\n", "prompt = hub.pull(\"hwchase17/openai-functions-agent\")\n", "tools = [introduce]\n", "agent = create_tool_calling_agent(llm, tools, prompt)\n", "agent_executor = AgentExecutor(agent=agent, tools=tools)"]}, {"cell_type": "code", "execution_count": 11, "id": "8246b287-22e3-4641-8d10-52d250558fb4", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\software\\python\\Lib\\site-packages\\langsmith\\client.py:241: LangSmithMissingAPIKeyWarning: API key must be provided when using hosted LangSmith API\n", "  warnings.warn(\n"]}], "source": ["from langchain.agents import create_react_agent, AgentExecutor\n", "\n", "prompt = hub.pull(\"hwchase17/react\")\n", "tools = [introduce]\n", "agent = create_react_agent(llm, tools, prompt)\n", "agent_executor = AgentExecutor(agent=agent, tools=tools, verbose=True)\n"]}, {"cell_type": "code", "execution_count": 12, "id": "5394ff59-6cf9-4457-886a-25776d6c76f4", "metadata": {}, "outputs": [{"data": {"text/plain": ["PromptTemplate(input_variables=['agent_scratchpad', 'input', 'tool_names', 'tools'], input_types={}, partial_variables={}, metadata={'lc_hub_owner': 'hwchase17', 'lc_hub_repo': 'react', 'lc_hub_commit_hash': 'd15fe3c426f1c4b3f37c9198853e4a86e20c425ca7f4752ec0c9b0e97ca7ea4d'}, template='Answer the following questions as best you can. You have access to the following tools:\\n\\n{tools}\\n\\nUse the following format:\\n\\nQuestion: the input question you must answer\\nThought: you should always think about what to do\\nAction: the action to take, should be one of [{tool_names}]\\nAction Input: the input to the action\\nObservation: the result of the action\\n... (this Thought/Action/Action Input/Observation can repeat N times)\\nThought: I now know the final answer\\nFinal Answer: the final answer to the original input question\\n\\nBegin!\\n\\nQuestion: {input}\\nThought:{agent_scratchpad}')"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["prompt"]}, {"cell_type": "code", "execution_count": 6, "id": "22bd6b8b-2d75-4fb3-ac0e-7d3e6acd1987", "metadata": {}, "outputs": [], "source": ["from langgraph.prebuilt import create_react_agent\n", "from langgraph.checkpoint.memory import MemorySaver\n", "from langchain_core.messages import HumanMessage\n", "\n", "memory = MemorySaver()\n", "\n", "system_prompt = \"You are a helpful bot named <PERSON>.\"\n", "graph = create_react_agent(\n", "    llm,\n", "    tools=[introduce],\n", "    checkpointer=memory,\n", "    prompt=system_prompt\n", ")\n", "config = {\"configurable\": {\"thread_id\": \"111\"}}\n", "input_message = HumanMessage(content=\"扶鹰是做什么的?\")\n", "\n", "for s in graph.stream(inputs, stream_mode=\"values\"):\n", "    message = s[\"messages\"][-1]\n", "    if isinstance(message, tuple):\n", "        print(message)\n", "    else:\n", "        message.pretty_print()"]}, {"cell_type": "code", "execution_count": null, "id": "117af174-09a1-4f2e-a035-f19df1d7d76b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 63, "id": "06e4a2a3-8738-47d7-b8e0-8803c1e71c87", "metadata": {}, "outputs": [], "source": ["from langgraph.graph import MessagesState\n", "\n", "class AgentState(MessagesState):\n", "    next: str"]}, {"cell_type": "code", "execution_count": 57, "id": "731a3a48-f4e3-438f-9a1d-bb999ecfa6cb", "metadata": {}, "outputs": [], "source": ["def chat1(state: AgentState):\n", "    messages = state['messages']"]}, {"cell_type": "code", "execution_count": 58, "id": "b5d7876b-8332-431d-9d11-d9862e5c7809", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ed31b09a-3ab3-4f53-90d9-a26ee1703ce0", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 5}
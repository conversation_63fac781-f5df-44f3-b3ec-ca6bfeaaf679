{"cells": [{"cell_type": "code", "execution_count": 1, "id": "69eec43f-dfc7-4318-b653-0f908d0f3876", "metadata": {}, "outputs": [], "source": ["import os\n", "from dotenv import load_dotenv, find_dotenv\n", "\n", "load_dotenv()\n", "from langchain_openai import ChatOpenAI\n", "import os\n", "\n", "llm = ChatOpenAI(\n", "    api_key=os.getenv('MODAL_API_KEY'),\n", "    base_url=os.getenv('MODAL_URL'),\n", "    model=os.getenv('MODAL_NAME'),\n", "    # other params...\n", ")"]}, {"cell_type": "code", "execution_count": 2, "id": "1f6b81f2-5b8a-4709-8973-cd2604585106", "metadata": {}, "outputs": [], "source": ["from typing import Annotated\n", "from langgraph.graph import StateGraph\n", "from typing_extensions import TypedDict\n", "from langgraph.graph.message import add_messages\n", "\n", "class State(TypedDict):\n", "    messages: Annotated[list, add_messages]\n", "\n", "graph_builder = StateGraph(State)"]}, {"cell_type": "code", "execution_count": 3, "id": "4b200337-1610-4d6e-a06f-97233ca6bf2a", "metadata": {}, "outputs": [], "source": ["def chatbot(state: State):\n", "    return {\"messages\": [llm.invoke(state[\"messages\"])]}"]}, {"cell_type": "code", "execution_count": null, "id": "a6576d04-db43-4a7b-bc73-847184eab55c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 4, "id": "7f0c51fc-a1c5-4f1b-b249-580f6cc9109e", "metadata": {}, "outputs": [], "source": ["from langgraph.graph import START, END\n", "\n", "graph_builder.add_node(\"chatbot\", chatbot)\n", "graph_builder.add_edge(START, \"chatbot\")\n", "graph_builder.add_edge(\"chatbot\", END)\n", "\n", "graph = graph_builder.compile()"]}, {"cell_type": "code", "execution_count": null, "id": "cd47c326-33c8-4606-bf92-e39f1d57b4aa", "metadata": {}, "outputs": [], "source": ["!pip install pyppeteer ipython"]}, {"cell_type": "code", "execution_count": 5, "id": "3f3bb41b-034e-4a72-9d70-51915119ec08", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import Image, display\n", "\n", "display(Image(graph.get_graph(xray=True).draw_mermaid_png()))"]}, {"cell_type": "code", "execution_count": 5, "id": "c66c8dfc-59bb-4167-820c-774e55d3cae2", "metadata": {}, "outputs": [{"ename": "NotFoundError", "evalue": "Error code: 404 - {'error': {'message': 'The model `deepseek-V3` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': 'a149d21e-646b-90bf-8285-9ae270cda817'}", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNotFoundError\u001b[0m                             <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[5], line 3\u001b[0m\n\u001b[0;32m      1\u001b[0m input_message \u001b[38;5;241m=\u001b[39m {\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmessages\u001b[39m\u001b[38;5;124m\"\u001b[39m: [\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m你是谁?\u001b[39m\u001b[38;5;124m\"\u001b[39m]}\n\u001b[1;32m----> 3\u001b[0m \u001b[43mgraph\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43minvoke\u001b[49m\u001b[43m(\u001b[49m\u001b[43minput_message\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mC:\\software\\python\\Lib\\site-packages\\langgraph\\pregel\\__init__.py:2069\u001b[0m, in \u001b[0;36mPregel.invoke\u001b[1;34m(self, input, config, stream_mode, output_keys, interrupt_before, interrupt_after, debug, **kwargs)\u001b[0m\n\u001b[0;32m   2067\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m   2068\u001b[0m     chunks \u001b[38;5;241m=\u001b[39m []\n\u001b[1;32m-> 2069\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m chunk \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstream(\n\u001b[0;32m   2070\u001b[0m     \u001b[38;5;28minput\u001b[39m,\n\u001b[0;32m   2071\u001b[0m     config,\n\u001b[0;32m   2072\u001b[0m     stream_mode\u001b[38;5;241m=\u001b[39mstream_mode,\n\u001b[0;32m   2073\u001b[0m     output_keys\u001b[38;5;241m=\u001b[39moutput_keys,\n\u001b[0;32m   2074\u001b[0m     interrupt_before\u001b[38;5;241m=\u001b[39minterrupt_before,\n\u001b[0;32m   2075\u001b[0m     interrupt_after\u001b[38;5;241m=\u001b[39minterrupt_after,\n\u001b[0;32m   2076\u001b[0m     debug\u001b[38;5;241m=\u001b[39mdebug,\n\u001b[0;32m   2077\u001b[0m     \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs,\n\u001b[0;32m   2078\u001b[0m ):\n\u001b[0;32m   2079\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m stream_mode \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mvalues\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[0;32m   2080\u001b[0m         latest \u001b[38;5;241m=\u001b[39m chunk\n", "File \u001b[1;32mC:\\software\\python\\Lib\\site-packages\\langgraph\\pregel\\__init__.py:1724\u001b[0m, in \u001b[0;36mPregel.stream\u001b[1;34m(self, input, config, stream_mode, output_keys, interrupt_before, interrupt_after, debug, subgraphs)\u001b[0m\n\u001b[0;32m   1718\u001b[0m     \u001b[38;5;66;03m# Similarly to Bulk Synchronous Parallel / Pregel model\u001b[39;00m\n\u001b[0;32m   1719\u001b[0m     \u001b[38;5;66;03m# computation proceeds in steps, while there are channel updates.\u001b[39;00m\n\u001b[0;32m   1720\u001b[0m     \u001b[38;5;66;03m# Channel updates from step N are only visible in step N+1\u001b[39;00m\n\u001b[0;32m   1721\u001b[0m     \u001b[38;5;66;03m# channels are guaranteed to be immutable for the duration of the step,\u001b[39;00m\n\u001b[0;32m   1722\u001b[0m     \u001b[38;5;66;03m# with channel updates applied only at the transition between steps.\u001b[39;00m\n\u001b[0;32m   1723\u001b[0m     \u001b[38;5;28;01mwhile\u001b[39;00m loop\u001b[38;5;241m.\u001b[39mtick(input_keys\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39minput_channels):\n\u001b[1;32m-> 1724\u001b[0m         \u001b[38;5;28;01mfor\u001b[39;00m _ \u001b[38;5;129;01min\u001b[39;00m runner\u001b[38;5;241m.\u001b[39mtick(\n\u001b[0;32m   1725\u001b[0m             loop\u001b[38;5;241m.\u001b[39mtasks\u001b[38;5;241m.\u001b[39mvalues(),\n\u001b[0;32m   1726\u001b[0m             timeout\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstep_timeout,\n\u001b[0;32m   1727\u001b[0m             retry_policy\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mretry_policy,\n\u001b[0;32m   1728\u001b[0m             get_waiter\u001b[38;5;241m=\u001b[39mget_waiter,\n\u001b[0;32m   1729\u001b[0m         ):\n\u001b[0;32m   1730\u001b[0m             \u001b[38;5;66;03m# emit output\u001b[39;00m\n\u001b[0;32m   1731\u001b[0m             \u001b[38;5;28;01myield from\u001b[39;00m output()\n\u001b[0;32m   1732\u001b[0m \u001b[38;5;66;03m# emit output\u001b[39;00m\n", "File \u001b[1;32mC:\\software\\python\\Lib\\site-packages\\langgraph\\pregel\\runner.py:230\u001b[0m, in \u001b[0;36mPregelRunner.tick\u001b[1;34m(self, tasks, reraise, timeout, retry_policy, get_waiter)\u001b[0m\n\u001b[0;32m    228\u001b[0m t \u001b[38;5;241m=\u001b[39m tasks[\u001b[38;5;241m0\u001b[39m]\n\u001b[0;32m    229\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m--> 230\u001b[0m     \u001b[43mrun_with_retry\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    231\u001b[0m \u001b[43m        \u001b[49m\u001b[43mt\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    232\u001b[0m \u001b[43m        \u001b[49m\u001b[43mretry_policy\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    233\u001b[0m \u001b[43m        \u001b[49m\u001b[43mconfigurable\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m{\u001b[49m\n\u001b[0;32m    234\u001b[0m \u001b[43m            \u001b[49m\u001b[43mCONFIG_KEY_SEND\u001b[49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mpartial\u001b[49m\u001b[43m(\u001b[49m\u001b[43mwriter\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mt\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    235\u001b[0m \u001b[43m            \u001b[49m\u001b[43mCONFIG_KEY_CALL\u001b[49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mpartial\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcall\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mt\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    236\u001b[0m \u001b[43m        \u001b[49m\u001b[43m}\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    237\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    238\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcommit(t, \u001b[38;5;28;01mNone\u001b[39;00m)\n\u001b[0;32m    239\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m exc:\n", "File \u001b[1;32mC:\\software\\python\\Lib\\site-packages\\langgraph\\pregel\\retry.py:40\u001b[0m, in \u001b[0;36mrun_with_retry\u001b[1;34m(task, retry_policy, configurable)\u001b[0m\n\u001b[0;32m     38\u001b[0m     task\u001b[38;5;241m.\u001b[39mwrites\u001b[38;5;241m.\u001b[39mclear()\n\u001b[0;32m     39\u001b[0m     \u001b[38;5;66;03m# run the task\u001b[39;00m\n\u001b[1;32m---> 40\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mtask\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mproc\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43minvoke\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtask\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43minput\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     41\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m ParentCommand \u001b[38;5;28;01mas\u001b[39;00m exc:\n\u001b[0;32m     42\u001b[0m     ns: \u001b[38;5;28mstr\u001b[39m \u001b[38;5;241m=\u001b[39m config[CONF][CONFIG_KEY_CHECKPOINT_NS]\n", "File \u001b[1;32mC:\\software\\python\\Lib\\site-packages\\langgraph\\utils\\runnable.py:506\u001b[0m, in \u001b[0;36mRunnableSeq.invoke\u001b[1;34m(self, input, config, **kwargs)\u001b[0m\n\u001b[0;32m    502\u001b[0m config \u001b[38;5;241m=\u001b[39m patch_config(\n\u001b[0;32m    503\u001b[0m     config, callbacks\u001b[38;5;241m=\u001b[39mrun_manager\u001b[38;5;241m.\u001b[39mget_child(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mseq:step:\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mi\u001b[38;5;250m \u001b[39m\u001b[38;5;241m+\u001b[39m\u001b[38;5;250m \u001b[39m\u001b[38;5;241m1\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m    504\u001b[0m )\n\u001b[0;32m    505\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m i \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m0\u001b[39m:\n\u001b[1;32m--> 506\u001b[0m     \u001b[38;5;28minput\u001b[39m \u001b[38;5;241m=\u001b[39m \u001b[43mstep\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43minvoke\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43minput\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    507\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m    508\u001b[0m     \u001b[38;5;28minput\u001b[39m \u001b[38;5;241m=\u001b[39m step\u001b[38;5;241m.\u001b[39minvoke(\u001b[38;5;28minput\u001b[39m, config)\n", "File \u001b[1;32mC:\\software\\python\\Lib\\site-packages\\langgraph\\utils\\runnable.py:270\u001b[0m, in \u001b[0;36mRunnableCallable.invoke\u001b[1;34m(self, input, config, **kwargs)\u001b[0m\n\u001b[0;32m    268\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m    269\u001b[0m     context\u001b[38;5;241m.\u001b[39mrun(_set_config_context, config)\n\u001b[1;32m--> 270\u001b[0m     ret \u001b[38;5;241m=\u001b[39m \u001b[43mcontext\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrun\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfunc\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    271\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(ret, Runnable) \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mrecurse:\n\u001b[0;32m    272\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m ret\u001b[38;5;241m.\u001b[39minvoke(\u001b[38;5;28minput\u001b[39m, config)\n", "Cell \u001b[1;32mIn[3], line 2\u001b[0m, in \u001b[0;36mchatbot\u001b[1;34m(state)\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mchatbot\u001b[39m(state: State):\n\u001b[1;32m----> 2\u001b[0m     \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m {\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmessages\u001b[39m\u001b[38;5;124m\"\u001b[39m: [\u001b[43mllm\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43minvoke\u001b[49m\u001b[43m(\u001b[49m\u001b[43mstate\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mmessages\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m)\u001b[49m]}\n", "File \u001b[1;32mC:\\software\\python\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py:284\u001b[0m, in \u001b[0;36mBaseChatModel.invoke\u001b[1;34m(self, input, config, stop, **kwargs)\u001b[0m\n\u001b[0;32m    273\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21minvoke\u001b[39m(\n\u001b[0;32m    274\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[0;32m    275\u001b[0m     \u001b[38;5;28minput\u001b[39m: LanguageModelInput,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    279\u001b[0m     \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs: Any,\n\u001b[0;32m    280\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m BaseMessage:\n\u001b[0;32m    281\u001b[0m     config \u001b[38;5;241m=\u001b[39m ensure_config(config)\n\u001b[0;32m    282\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m cast(\n\u001b[0;32m    283\u001b[0m         ChatGeneration,\n\u001b[1;32m--> 284\u001b[0m         \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgenerate_prompt\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    285\u001b[0m \u001b[43m            \u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_convert_input\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43minput\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    286\u001b[0m \u001b[43m            \u001b[49m\u001b[43mstop\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstop\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    287\u001b[0m \u001b[43m            \u001b[49m\u001b[43mcallbacks\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mconfig\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mcallbacks\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    288\u001b[0m \u001b[43m            \u001b[49m\u001b[43mtags\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mconfig\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mtags\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    289\u001b[0m \u001b[43m            \u001b[49m\u001b[43mmetadata\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mconfig\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mmetadata\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    290\u001b[0m \u001b[43m            \u001b[49m\u001b[43mrun_name\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mconfig\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mrun_name\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    291\u001b[0m \u001b[43m            \u001b[49m\u001b[43mrun_id\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mconfig\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mpop\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mrun_id\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    292\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    293\u001b[0m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241m.\u001b[39mgenerations[\u001b[38;5;241m0\u001b[39m][\u001b[38;5;241m0\u001b[39m],\n\u001b[0;32m    294\u001b[0m     )\u001b[38;5;241m.\u001b[39mmessage\n", "File \u001b[1;32mC:\\software\\python\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py:860\u001b[0m, in \u001b[0;36mBaseChatModel.generate_prompt\u001b[1;34m(self, prompts, stop, callbacks, **kwargs)\u001b[0m\n\u001b[0;32m    852\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mgenerate_prompt\u001b[39m(\n\u001b[0;32m    853\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[0;32m    854\u001b[0m     prompts: \u001b[38;5;28mlist\u001b[39m[PromptValue],\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    857\u001b[0m     \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs: Any,\n\u001b[0;32m    858\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m LLMResult:\n\u001b[0;32m    859\u001b[0m     prompt_messages \u001b[38;5;241m=\u001b[39m [p\u001b[38;5;241m.\u001b[39mto_messages() \u001b[38;5;28;01mfor\u001b[39;00m p \u001b[38;5;129;01min\u001b[39;00m prompts]\n\u001b[1;32m--> 860\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgenerate\u001b[49m\u001b[43m(\u001b[49m\u001b[43mprompt_messages\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstop\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstop\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcallbacks\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcallbacks\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mC:\\software\\python\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py:690\u001b[0m, in \u001b[0;36mBaseChatModel.generate\u001b[1;34m(self, messages, stop, callbacks, tags, metadata, run_name, run_id, **kwargs)\u001b[0m\n\u001b[0;32m    687\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m i, m \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28menumerate\u001b[39m(messages):\n\u001b[0;32m    688\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m    689\u001b[0m         results\u001b[38;5;241m.\u001b[39mappend(\n\u001b[1;32m--> 690\u001b[0m             \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_generate_with_cache\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    691\u001b[0m \u001b[43m                \u001b[49m\u001b[43mm\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    692\u001b[0m \u001b[43m                \u001b[49m\u001b[43mstop\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstop\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    693\u001b[0m \u001b[43m                \u001b[49m\u001b[43mrun_manager\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrun_managers\u001b[49m\u001b[43m[\u001b[49m\u001b[43mi\u001b[49m\u001b[43m]\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mrun_managers\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01melse\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[0;32m    694\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    695\u001b[0m \u001b[43m            \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    696\u001b[0m         )\n\u001b[0;32m    697\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mBaseException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[0;32m    698\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m run_managers:\n", "File \u001b[1;32mC:\\software\\python\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py:925\u001b[0m, in \u001b[0;36mBaseChatModel._generate_with_cache\u001b[1;34m(self, messages, stop, run_manager, **kwargs)\u001b[0m\n\u001b[0;32m    923\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m    924\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m inspect\u001b[38;5;241m.\u001b[39msignature(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_generate)\u001b[38;5;241m.\u001b[39mparameters\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mrun_manager\u001b[39m\u001b[38;5;124m\"\u001b[39m):\n\u001b[1;32m--> 925\u001b[0m         result \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_generate\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    926\u001b[0m \u001b[43m            \u001b[49m\u001b[43mmessages\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstop\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstop\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mrun_manager\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrun_manager\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\n\u001b[0;32m    927\u001b[0m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    928\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m    929\u001b[0m         result \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_generate(messages, stop\u001b[38;5;241m=\u001b[39mstop, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n", "File \u001b[1;32mC:\\software\\python\\Lib\\site-packages\\langchain_openai\\chat_models\\base.py:790\u001b[0m, in \u001b[0;36mBaseChatOpenAI._generate\u001b[1;34m(self, messages, stop, run_manager, **kwargs)\u001b[0m\n\u001b[0;32m    788\u001b[0m     generation_info \u001b[38;5;241m=\u001b[39m {\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mheaders\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;28mdict\u001b[39m(raw_response\u001b[38;5;241m.\u001b[39mheaders)}\n\u001b[0;32m    789\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m--> 790\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mclient\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcreate\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mpayload\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    791\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_create_chat_result(response, generation_info)\n", "File \u001b[1;32mC:\\software\\python\\Lib\\site-packages\\openai\\_utils\\_utils.py:279\u001b[0m, in \u001b[0;36mrequired_args.<locals>.inner.<locals>.wrapper\u001b[1;34m(*args, **kwargs)\u001b[0m\n\u001b[0;32m    277\u001b[0m             msg \u001b[38;5;241m=\u001b[39m \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mMissing required argument: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mquote(missing[\u001b[38;5;241m0\u001b[39m])\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    278\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m(msg)\n\u001b[1;32m--> 279\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mC:\\software\\python\\Lib\\site-packages\\openai\\resources\\chat\\completions.py:863\u001b[0m, in \u001b[0;36mCompletions.create\u001b[1;34m(self, messages, model, audio, frequency_penalty, function_call, functions, logit_bias, logprobs, max_completion_tokens, max_tokens, metadata, modalities, n, parallel_tool_calls, prediction, presence_penalty, reasoning_effort, response_format, seed, service_tier, stop, store, stream, stream_options, temperature, tool_choice, tools, top_logprobs, top_p, user, extra_headers, extra_query, extra_body, timeout)\u001b[0m\n\u001b[0;32m    821\u001b[0m \u001b[38;5;129m@required_args\u001b[39m([\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmessages\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmodel\u001b[39m\u001b[38;5;124m\"\u001b[39m], [\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmessages\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmodel\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstream\u001b[39m\u001b[38;5;124m\"\u001b[39m])\n\u001b[0;32m    822\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mcreate\u001b[39m(\n\u001b[0;32m    823\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    860\u001b[0m     timeout: \u001b[38;5;28mfloat\u001b[39m \u001b[38;5;241m|\u001b[39m httpx\u001b[38;5;241m.\u001b[39mTimeout \u001b[38;5;241m|\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;241m|\u001b[39m NotGiven \u001b[38;5;241m=\u001b[39m NOT_GIVEN,\n\u001b[0;32m    861\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m ChatCompletion \u001b[38;5;241m|\u001b[39m Stream[ChatCompletionChunk]:\n\u001b[0;32m    862\u001b[0m     validate_response_format(response_format)\n\u001b[1;32m--> 863\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_post\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    864\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m/chat/completions\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[0;32m    865\u001b[0m \u001b[43m        \u001b[49m\u001b[43mbody\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmaybe_transform\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    866\u001b[0m \u001b[43m            \u001b[49m\u001b[43m{\u001b[49m\n\u001b[0;32m    867\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mmessages\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmessages\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    868\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mmodel\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmodel\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    869\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43maudio\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43maudio\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    870\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mfrequency_penalty\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mfrequency_penalty\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    871\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mfunction_call\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mfunction_call\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    872\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mfunctions\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mfunctions\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    873\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mlogit_bias\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mlogit_bias\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    874\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mlogprobs\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mlogprobs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    875\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mmax_completion_tokens\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmax_completion_tokens\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    876\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mmax_tokens\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmax_tokens\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    877\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mmetadata\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmetadata\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    878\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mmodalities\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmodalities\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    879\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mn\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mn\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    880\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mparallel_tool_calls\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mparallel_tool_calls\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    881\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mprediction\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mprediction\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    882\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mpresence_penalty\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mpresence_penalty\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    883\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mreasoning_effort\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mreasoning_effort\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    884\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mresponse_format\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mresponse_format\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    885\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mseed\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mseed\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    886\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mservice_tier\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mservice_tier\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    887\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mstop\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mstop\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    888\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mstore\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mstore\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    889\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mstream\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    890\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mstream_options\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream_options\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    891\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mtemperature\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtemperature\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    892\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mtool_choice\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtool_choice\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    893\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mtools\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtools\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    894\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mtop_logprobs\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtop_logprobs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    895\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mtop_p\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtop_p\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    896\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43muser\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43muser\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    897\u001b[0m \u001b[43m            \u001b[49m\u001b[43m}\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    898\u001b[0m \u001b[43m            \u001b[49m\u001b[43mcompletion_create_params\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mCompletionCreateParams\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    899\u001b[0m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    900\u001b[0m \u001b[43m        \u001b[49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmake_request_options\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    901\u001b[0m \u001b[43m            \u001b[49m\u001b[43mextra_headers\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mextra_headers\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mextra_query\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mextra_query\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mextra_body\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mextra_body\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout\u001b[49m\n\u001b[0;32m    902\u001b[0m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    903\u001b[0m \u001b[43m        \u001b[49m\u001b[43mcast_to\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mChatCompletion\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    904\u001b[0m \u001b[43m        \u001b[49m\u001b[43mstream\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstream\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[0;32m    905\u001b[0m \u001b[43m        \u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mStream\u001b[49m\u001b[43m[\u001b[49m\u001b[43mChatCompletionChunk\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    906\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mC:\\software\\python\\Lib\\site-packages\\openai\\_base_client.py:1283\u001b[0m, in \u001b[0;36mSyncAPIClient.post\u001b[1;34m(self, path, cast_to, body, options, files, stream, stream_cls)\u001b[0m\n\u001b[0;32m   1269\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mpost\u001b[39m(\n\u001b[0;32m   1270\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[0;32m   1271\u001b[0m     path: \u001b[38;5;28mstr\u001b[39m,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m   1278\u001b[0m     stream_cls: \u001b[38;5;28mtype\u001b[39m[_StreamT] \u001b[38;5;241m|\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[0;32m   1279\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m ResponseT \u001b[38;5;241m|\u001b[39m _StreamT:\n\u001b[0;32m   1280\u001b[0m     opts \u001b[38;5;241m=\u001b[39m FinalRequestOptions\u001b[38;5;241m.\u001b[39mconstruct(\n\u001b[0;32m   1281\u001b[0m         method\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mpost\u001b[39m\u001b[38;5;124m\"\u001b[39m, url\u001b[38;5;241m=\u001b[39mpath, json_data\u001b[38;5;241m=\u001b[39mbody, files\u001b[38;5;241m=\u001b[39mto_httpx_files(files), \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39moptions\n\u001b[0;32m   1282\u001b[0m     )\n\u001b[1;32m-> 1283\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m cast(ResponseT, \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcast_to\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mopts\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstream\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstream_cls\u001b[49m\u001b[43m)\u001b[49m)\n", "File \u001b[1;32mC:\\software\\python\\Lib\\site-packages\\openai\\_base_client.py:960\u001b[0m, in \u001b[0;36mSyncAPIClient.request\u001b[1;34m(self, cast_to, options, remaining_retries, stream, stream_cls)\u001b[0m\n\u001b[0;32m    957\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m    958\u001b[0m     retries_taken \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m0\u001b[39m\n\u001b[1;32m--> 960\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_request\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    961\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcast_to\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcast_to\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    962\u001b[0m \u001b[43m    \u001b[49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    963\u001b[0m \u001b[43m    \u001b[49m\u001b[43mstream\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstream\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    964\u001b[0m \u001b[43m    \u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstream_cls\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    965\u001b[0m \u001b[43m    \u001b[49m\u001b[43mretries_taken\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mretries_taken\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    966\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mC:\\software\\python\\Lib\\site-packages\\openai\\_base_client.py:1064\u001b[0m, in \u001b[0;36mSyncAPIClient._request\u001b[1;34m(self, cast_to, options, retries_taken, stream, stream_cls)\u001b[0m\n\u001b[0;32m   1061\u001b[0m         err\u001b[38;5;241m.\u001b[39mresponse\u001b[38;5;241m.\u001b[39mread()\n\u001b[0;32m   1063\u001b[0m     log\u001b[38;5;241m.\u001b[39mdebug(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mRe-raising status error\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m-> 1064\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_make_status_error_from_response(err\u001b[38;5;241m.\u001b[39mresponse) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[0;32m   1066\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_process_response(\n\u001b[0;32m   1067\u001b[0m     cast_to\u001b[38;5;241m=\u001b[39mcast_to,\n\u001b[0;32m   1068\u001b[0m     options\u001b[38;5;241m=\u001b[39moptions,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m   1072\u001b[0m     retries_taken\u001b[38;5;241m=\u001b[39mretries_taken,\n\u001b[0;32m   1073\u001b[0m )\n", "\u001b[1;31mNotFoundError\u001b[0m: Error code: 404 - {'error': {'message': 'The model `deepseek-V3` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': 'a149d21e-646b-90bf-8285-9ae270cda817'}", "\u001b[0mDuring task with name 'chatbot' and id 'a42fd957-6d49-2f85-ddf8-188c1f9eaae3'"]}], "source": ["input_message = {\"messages\": [\"你是谁?\"]}\n", "\n", "graph.invoke(input_message)"]}, {"cell_type": "code", "execution_count": null, "id": "80b28d60-89de-4ebf-a0d2-cbff4721c1d5", "metadata": {}, "outputs": [], "source": ["def stream_graph_updates(user_input: str):\n", "    for event in graph.stream({\"messages\": [(\"user\", user_input)]):\n", "        for value in event.values():\n", "            print(\"模型回复\", value[\"messages\"][-1].content)\n", "\n", "while True:\n", "    try:\n", "        user_input = input(\"用户提问\")\n", "        if user_input.lower() in [\"退出\"]:\n", "            print(\"再见\")\n", "            break\n", "        stream_graph_updates(user_input)\n", "    except:\n", "        break"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "code", "execution_count": 1, "id": "8ea1a3af-3d1a-4a2a-a54c-526eea8e52cb", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "import pandas as pd\n", "import tiktoken\n", "\n", "from graphrag.query.context_builder.entity_extraction import EntityVectorStoreKey\n", "from graphrag.query.indexer_adapters import (\n", "    read_indexer_covariates,\n", "    read_indexer_entities,\n", "    read_indexer_relationships,\n", "    read_indexer_reports,\n", "    read_indexer_text_units,\n", ")\n", "from graphrag.query.llm.oai.chat_openai import ChatOpenAI\n", "from graphrag.query.llm.oai.embedding import OpenAIEmbedding\n", "from graphrag.query.llm.oai.typing import OpenaiApiType\n", "from graphrag.query.question_gen.local_gen import LocalQuestionGen\n", "from graphrag.query.structured_search.local_search.mixed_context import (\n", "    LocalSearchMixedContext,\n", ")\n", "from graphrag.query.structured_search.local_search.search import LocalSearch\n", "from graphrag.vector_stores.lancedb import LanceDBVectorStore"]}, {"cell_type": "code", "execution_count": 2, "id": "689b0fcb-723c-4ca5-9d48-22126a65a3cb", "metadata": {}, "outputs": [], "source": ["INPUT_DIR = \"C:/workspace/graph-rag/test2/output\"\n", "LANCEDB_URI = f\"{INPUT_DIR}/lancedb\"\n", "\n", "COMMUNITY_REPORT_TABLE = \"create_final_community_reports\"\n", "ENTITY_TABLE = \"create_final_nodes\"\n", "ENTITY_EMBEDDING_TABLE = \"create_final_entities\"\n", "COMMUNITY_TABLE = \"create_final_communities\"\n", "RELATIONSHIP_TABLE = \"create_final_relationships\"\n", "#COVARIATE_TABLE = \"create_final_covariates\"\n", "TEXT_UNIT_TABLE = \"create_final_text_units\"\n", "COMMUNITY_LEVEL = 2"]}, {"cell_type": "code", "execution_count": 3, "id": "580bfc5d-1215-4cd5-b6ad-f5ac6830afac", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Entity count: 4435\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>human_readable_id</th>\n", "      <th>title</th>\n", "      <th>community</th>\n", "      <th>level</th>\n", "      <th>degree</th>\n", "      <th>x</th>\n", "      <th>y</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>aa768ffe-6c70-403e-9dd7-7ed7d8605ca6</td>\n", "      <td>0</td>\n", "      <td>社交情商课</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>aa768ffe-6c70-403e-9dd7-7ed7d8605ca6</td>\n", "      <td>0</td>\n", "      <td>社交情商课</td>\n", "      <td>62</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>aa768ffe-6c70-403e-9dd7-7ed7d8605ca6</td>\n", "      <td>0</td>\n", "      <td>社交情商课</td>\n", "      <td>291</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>bc443580-09a3-415f-a8cf-1f907d6d635b</td>\n", "      <td>1</td>\n", "      <td>父母</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>48</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>bc443580-09a3-415f-a8cf-1f907d6d635b</td>\n", "      <td>1</td>\n", "      <td>父母</td>\n", "      <td>47</td>\n", "      <td>1</td>\n", "      <td>48</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                     id  human_readable_id  title  community  \\\n", "0  aa768ffe-6c70-403e-9dd7-7ed7d8605ca6                  0  社交情商课          4   \n", "1  aa768ffe-6c70-403e-9dd7-7ed7d8605ca6                  0  社交情商课         62   \n", "2  aa768ffe-6c70-403e-9dd7-7ed7d8605ca6                  0  社交情商课        291   \n", "3  bc443580-09a3-415f-a8cf-1f907d6d635b                  1     父母          2   \n", "4  bc443580-09a3-415f-a8cf-1f907d6d635b                  1     父母         47   \n", "\n", "   level  degree    x    y  \n", "0      0       2  0.0  0.0  \n", "1      1       2  0.0  0.0  \n", "2      2       2  0.0  0.0  \n", "3      0      48  0.0  0.0  \n", "4      1      48  0.0  0.0  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["entity_df = pd.read_parquet(f\"{INPUT_DIR}/{ENTITY_TABLE}.parquet\")\n", "entity_embedding_df = pd.read_parquet(f\"{INPUT_DIR}/{ENTITY_EMBEDDING_TABLE}.parquet\")\n", "\n", "entities = read_indexer_entities(entity_df, entity_embedding_df, COMMUNITY_LEVEL)\n", "description_embedding_store = LanceDBVectorStore(\n", "    collection_name=\"default-entity-description\",\n", ")\n", "description_embedding_store.connect(db_uri=LANCEDB_URI)\n", "\n", "print(f\"Entity count: {len(entity_df)}\")\n", "entity_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d80c9a18-7613-44d6-8018-9c93738a1f8c", "metadata": {}, "outputs": [], "source": ["relationship_df = pd.read_parquet(f\"{INPUT_DIR}/{RELATIONSHIP_TABLE}.parquet\")\n", "relationships = read_indexer_relationships(relationship_df)"]}, {"cell_type": "code", "execution_count": null, "id": "20ef0eac-9f24-4559-aef9-fcebb3e796fd", "metadata": {}, "outputs": [], "source": ["report_df = pd.read_parquet(f\"{INPUT_DIR}/{COMMUNITY_REPORT_TABLE}.parquet\")\n", "reports = read_indexer_reports(report_df, entity_df, COMMUNITY_LEVEL)"]}, {"cell_type": "code", "execution_count": null, "id": "354cc0a5-7ef8-4a96-b557-af6e6925ee24", "metadata": {}, "outputs": [], "source": ["text_unit_df = pd.read_parquet(f\"{INPUT_DIR}/{TEXT_UNIT_TABLE}.parquet\")\n", "text_units = read_indexer_text_units(text_unit_df)"]}, {"cell_type": "code", "execution_count": 7, "id": "986321d5-fc0f-4d1c-95b4-bdec839be6bf", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "from dotenv import load_dotenv, find_dotenv\n", "\n", "load_dotenv()"]}, {"cell_type": "code", "execution_count": 8, "id": "b77de4fb-7c7e-4f59-959e-e6a3284f5886", "metadata": {}, "outputs": [], "source": ["from graphrag.query.llm.oai.chat_openai import ChatOpenAI\n", "from graphrag.query.llm.oai.embedding import OpenAIEmbedding\n", "import os\n", "\n", "llm = ChatOpenAI(\n", "    api_key=os.getenv('MODAL_API_KEY'),\n", "    api_base=os.getenv('MODAL_URL'),\n", "    model=os.getenv('MODAL_NAME'),\n", "    api_type=OpenaiApiType.OpenAI,\n", "    max_retries=20,\n", "    # other params...\n", ")"]}, {"cell_type": "code", "execution_count": 9, "id": "027b4e74-aa6e-4493-b936-b7c07fe2f99f", "metadata": {}, "outputs": [{"data": {"text/plain": ["'deepseek-v3'"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["os.getenv('MODAL_NAME')"]}, {"cell_type": "code", "execution_count": 10, "id": "c8423334-77fc-4c52-9012-088061e2040c", "metadata": {}, "outputs": [], "source": ["embedding_model = \"text-embedding-v3\"\n", "text_embedder = OpenAIEmbedding(\n", "    api_key=os.getenv('MODAL_API_KEY'),\n", "    api_base=os.getenv('MODAL_API_URL'),\n", "    api_type=OpenaiApiType.OpenAI,\n", "    model=embedding_model,\n", "    deployment_name=embedding_model,\n", "    max_retries=20,\n", ")"]}, {"cell_type": "code", "execution_count": 11, "id": "635bc892-7bd1-4db4-8861-e87471a43369", "metadata": {}, "outputs": [], "source": ["token_encoder = tiktoken.get_encoding(\"cl100k_base\")\n", "\n", "context_builder = LocalSearchMixedContext(\n", "    community_reports=reports,\n", "    text_units=text_units,\n", "    entities=entities,\n", "    relationships=relationships,\n", "    # if you did not run covariates during indexing, set this to None\n", "    #covariates=covariates,\n", "    entity_text_embeddings=description_embedding_store,\n", "    embedding_vectorstore_key=EntityVectorStoreKey.ID,  # if the vectorstore uses entity title as ids, set this to EntityVectorStoreKey.TITLE\n", "    text_embedder=text_embedder,\n", "    token_encoder=token_encoder,\n", ")"]}, {"cell_type": "code", "execution_count": 12, "id": "c807adfd-947a-4184-94d9-57b5a7eb3706", "metadata": {}, "outputs": [], "source": ["\n", "local_context_params = {\n", "    \"text_unit_prop\": 0.5,\n", "    \"community_prop\": 0.1,\n", "    \"conversation_history_max_turns\": 5,\n", "    \"conversation_history_user_turns_only\": True,\n", "    \"top_k_mapped_entities\": 10,\n", "    \"top_k_relationships\": 10,\n", "    \"include_entity_rank\": True,\n", "    \"include_relationship_weight\": True,\n", "    \"include_community_rank\": <PERSON><PERSON><PERSON>,\n", "    \"return_candidate_context\": <PERSON><PERSON><PERSON>,\n", "    \"embedding_vectorstore_key\": EntityVectorStoreKey.ID,  # set this to EntityVectorStoreKey.TITLE if the vectorstore uses entity title as ids\n", "    \"max_tokens\": 12_000,  # change this based on the token limit you have on your model (if you are using a model with 8k limit, a good setting could be 5000)\n", "}\n", "\n", "llm_params = {\n", "    \"max_tokens\": 2_000,  # change this based on the token limit you have on your model (if you are using a model with 8k limit, a good setting could be 1000=1500)\n", "    \"temperature\": 0.0,\n", "}"]}, {"cell_type": "code", "execution_count": 13, "id": "5cc3525b-a83b-4c51-942d-15b6884233c7", "metadata": {}, "outputs": [], "source": ["search_engine = LocalSearch(\n", "    llm=llm,\n", "    context_builder=context_builder,\n", "    token_encoder=token_encoder,\n", "    llm_params=llm_params,\n", "    context_builder_params=local_context_params,\n", "    response_type=\"multiple paragraphs\",  # free form text describing the response type and format, can be anything, e.g. prioritized list, single paragraph, multiple paragraphs, multiple-page report\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "66f0a8a6-a2ea-4db0-b72c-eac86b6ce916", "metadata": {}, "outputs": [], "source": ["result = search_engine.search(\"父母总是唠叨怎么办？\")\n", "print(result.response)"]}, {"cell_type": "code", "execution_count": null, "id": "9696d92f-f57a-422d-821d-6fa1e819845f", "metadata": {}, "outputs": [], "source": ["result.context_text"]}, {"cell_type": "code", "execution_count": null, "id": "167efaac-3908-41af-91c5-7ab053202762", "metadata": {}, "outputs": [], "source": ["context_builder.build_context(query=\"王金海是谁？\")"]}, {"cell_type": "code", "execution_count": null, "id": "dc8a99ba-0611-45b4-b4e6-6c598775878f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "cf69956e-fa0a-497f-b289-a7eccc31e13a", "metadata": {}, "source": ["## Global模式"]}, {"cell_type": "code", "execution_count": 96, "id": "5b6abd4a-0f93-4b61-baf9-272ff213c077", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "import pandas as pd\n", "import tiktoken\n", "\n", "from graphrag.query.indexer_adapters import (\n", "    read_indexer_communities,\n", "    read_indexer_entities,\n", "    read_indexer_reports,\n", ")\n", "from graphrag.query.llm.oai.chat_openai import ChatOpenAI\n", "from graphrag.query.llm.oai.typing import OpenaiApiType\n", "from graphrag.query.structured_search.global_search.community_context import (\n", "    GlobalCommunityContext,\n", ")\n", "from graphrag.query.structured_search.global_search.search import GlobalSearch"]}, {"cell_type": "code", "execution_count": 100, "id": "0866738c-ef4e-4138-bcfd-f03307c8b8ba", "metadata": {}, "outputs": [], "source": ["community_df = pd.read_parquet(f\"{INPUT_DIR}/{COMMUNITY_TABLE}.parquet\")\n", "\n", "communities = read_indexer_communities(community_df, entity_df, report_df)"]}, {"cell_type": "code", "execution_count": 101, "id": "4ac713bc-f282-4e2d-8e85-02f6c55696e1", "metadata": {}, "outputs": [], "source": ["context_builder = GlobalCommunityContext(\n", "    community_reports=reports,\n", "    communities=communities,\n", "    entities=entities,  # default to None if you don't want to use community weights for ranking\n", "    token_encoder=token_encoder,\n", ")"]}, {"cell_type": "code", "execution_count": 102, "id": "bd6f3d4d-845f-4088-ab27-809408244df5", "metadata": {}, "outputs": [], "source": ["context_builder_params = {\n", "    \"use_community_summary\": False,  # False means using full community reports. True means using community short summaries.\n", "    \"shuffle_data\": True,\n", "    \"include_community_rank\": True,\n", "    \"min_community_rank\": 0,\n", "    \"community_rank_name\": \"rank\",\n", "    \"include_community_weight\": True,\n", "    \"community_weight_name\": \"occurrence weight\",\n", "    \"normalize_community_weight\": True,\n", "    \"max_tokens\": 12_000,  # change this based on the token limit you have on your model (if you are using a model with 8k limit, a good setting could be 5000)\n", "    \"context_name\": \"Reports\",\n", "}\n", "\n", "map_llm_params = {\n", "    \"max_tokens\": 1000,\n", "    \"temperature\": 0.0,\n", "    \"response_format\": {\"type\": \"json_object\"},\n", "}\n", "\n", "reduce_llm_params = {\n", "    \"max_tokens\": 2000,  # change this based on the token limit you have on your model (if you are using a model with 8k limit, a good setting could be 1000-1500)\n", "    \"temperature\": 0.0,\n", "}"]}, {"cell_type": "code", "execution_count": 103, "id": "aee5f469-24de-4a63-acf7-9038d5cd5dbe", "metadata": {}, "outputs": [], "source": ["search_engine = GlobalSearch(\n", "    llm=llm,\n", "    context_builder=context_builder,\n", "    token_encoder=token_encoder,\n", "    max_data_tokens=12_000,  # change this based on the token limit you have on your model (if you are using a model with 8k limit, a good setting could be 5000)\n", "    map_llm_params=map_llm_params,\n", "    reduce_llm_params=reduce_llm_params,\n", "    allow_general_knowledge=False,  # set this to True will add instruction to encourage the LLM to incorporate general knowledge in the response, which may increase hallucinations, but could be useful in some use cases.\n", "    json_mode=True,  # set this to False if your LLM model does not support JSON mode.\n", "    context_builder_params=context_builder_params,\n", "    concurrent_coroutines=32,\n", "    response_type=\"multiple paragraphs\",  # free form text describing the response type and format, can be anything, e.g. prioritized list, single paragraph, multiple paragraphs, multiple-page report\n", ")"]}, {"cell_type": "code", "execution_count": 104, "id": "1ab7155b-1f4e-48fb-bd87-d1c3ae9b395d", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["not expected dict type. type=<class 'str'>:\n", "Traceback (most recent call last):\n", "  File \"C:\\software\\python\\Lib\\site-packages\\graphrag\\query\\llm\\text_utils.py\", line 91, in try_parse_json_object\n", "    result = json.loads(input)\n", "             ^^^^^^^^^^^^^^^^^\n", "  File \"C:\\software\\python\\Lib\\json\\__init__.py\", line 346, in loads\n", "    return _default_decoder.decode(s)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"C:\\software\\python\\Lib\\json\\decoder.py\", line 337, in decode\n", "    obj, end = self.raw_decode(s, idx=_w(s, 0).end())\n", "               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"C:\\software\\python\\Lib\\json\\decoder.py\", line 355, in raw_decode\n", "    raise JSONDecodeError(\"Expecting value\", s, err.value) from None\n", "json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)\n", "Warning: All map responses have score 0 (i.e., no relevant information found from the dataset), returning a canned 'I do not know' answer. You can try enabling `allow_general_knowledge` to encourage the LLM to incorporate relevant general knowledge, at the risk of increasing hallucinations.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["I am sorry but I am unable to answer this question given the provided data.\n"]}], "source": ["result = await search_engine.asearch(\n", "    \"王金海是谁？\"\n", ")\n", "\n", "print(result.response)"]}, {"cell_type": "code", "execution_count": null, "id": "d445fae4-39e5-4d3b-b30a-e19e79ecb6bb", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 85, "id": "00779f88-a7f1-43f9-ae76-25bfdba8eb18", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "import pandas as pd\n", "import tiktoken\n", "\n", "from graphrag.query.context_builder.entity_extraction import EntityVectorStoreKey\n", "from graphrag.query.indexer_adapters import (\n", "    read_indexer_covariates,\n", "    read_indexer_entities,\n", "    read_indexer_relationships,\n", "    read_indexer_reports,\n", "    read_indexer_text_units,\n", ")\n", "from graphrag.query.llm.oai.chat_openai import ChatOpenAI\n", "from graphrag.query.llm.oai.embedding import OpenAIEmbedding\n", "from graphrag.query.llm.oai.typing import OpenaiApiType\n", "from graphrag.query.question_gen.local_gen import LocalQuestionGen\n", "from graphrag.query.structured_search.local_search.mixed_context import (\n", "    LocalSearchMixedContext,\n", ")\n", "from graphrag.query.structured_search.local_search.search import LocalSearch\n", "from graphrag.vector_stores.lancedb import LanceDBVectorStore\n", "    \n", "INPUT_DIR = \"C:/workspace/graph-rag/test1/output\"\n", "LANCEDB_URI = f\"{INPUT_DIR}/lancedb\"\n", "\n", "COMMUNITY_REPORT_TABLE = \"create_final_community_reports\"\n", "ENTITY_TABLE = \"create_final_nodes\"\n", "ENTITY_EMBEDDING_TABLE = \"create_final_entities\"\n", "COMMUNITY_TABLE = \"create_final_communities\"\n", "RELATIONSHIP_TABLE = \"create_final_relationships\"\n", "#COVARIATE_TABLE = \"create_final_covariates\"\n", "TEXT_UNIT_TABLE = \"create_final_text_units\"\n", "COMMUNITY_LEVEL = 2\n", "\n", "class MyRAG:\n", "    def __init__(self):\n", "        description_embedding_store = LanceDBVectorStore(\n", "            collection_name=\"default-entity-description\",\n", "        )\n", "        description_embedding_store.connect(db_uri=LANCEDB_URI)\n", "\n", "        entity_df = pd.read_parquet(f\"{INPUT_DIR}/{ENTITY_TABLE}.parquet\")\n", "        entity_embedding_df = pd.read_parquet(f\"{INPUT_DIR}/{ENTITY_EMBEDDING_TABLE}.parquet\")\n", "        entities = read_indexer_entities(entity_df, entity_embedding_df, COMMUNITY_LEVEL)\n", "\n", "        relationship_df = pd.read_parquet(f\"{INPUT_DIR}/{RELATIONSHIP_TABLE}.parquet\")\n", "        relationships = read_indexer_relationships(relationship_df)\n", "\n", "        report_df = pd.read_parquet(f\"{INPUT_DIR}/{COMMUNITY_REPORT_TABLE}.parquet\")\n", "        reports = read_indexer_reports(report_df, entity_df, COMMUNITY_LEVEL)\n", "\n", "        text_unit_df = pd.read_parquet(f\"{INPUT_DIR}/{TEXT_UNIT_TABLE}.parquet\")\n", "        text_units = read_indexer_text_units(text_unit_df)\n", "\n", "        embedding_model = \"text-embedding-v3\"\n", "        text_embedder = OpenAIEmbedding(\n", "            api_key=os.getenv('MODAL_API_KEY'),\n", "            api_base=os.getenv('MODAL_URL'),\n", "            api_type=OpenaiApiType.OpenAI,\n", "            model=embedding_model,\n", "            deployment_name=embedding_model,\n", "            max_retries=20,\n", "        )\n", "\n", "        token_encoder = tiktoken.get_encoding(\"cl100k_base\")\n", "\n", "        self.context_builder = LocalSearchMixedContext(\n", "            community_reports=reports,\n", "            text_units=text_units,\n", "            entities=entities,\n", "            relationships=relationships,\n", "            # if you did not run covariates during indexing, set this to None\n", "            #covariates=covariates,\n", "            entity_text_embeddings=description_embedding_store,\n", "            embedding_vectorstore_key=EntityVectorStoreKey.ID,  # if the vectorstore uses entity title as ids, set this to EntityVectorStoreKey.TITLE\n", "            text_embedder=text_embedder,\n", "            token_encoder=token_encoder,\n", "        )\n", "\n", "    def query(self, query: str) -> str: \n", "        context = self.context_builder.build_context(query)\n", "        return parse_res(context.context_chunks)\n", "    \n", "    def parse_res(res: str):\n", "        sections = res.split('\\n\\n\\n')  \n", "        source_content = \"\"\n", "        for section in sections:\n", "            if section.strip().startswith(\"-----Sources-----\"): \n", "                source_content = section\n", "                break\n", "        \n", "        content_arr = source_content.split('|')\n", "        content = content_arr[-1:]\n", "        return content[0]"]}, {"cell_type": "code", "execution_count": 86, "id": "62fae0fe-b2f3-4a1d-ab0c-aa11945fb3ca", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["杭州扶鹰教育科技有限公司是一家家庭教育企业，坐落于杭州上城区，在创始人王金海博士的带领下，致力于打造落地的家庭教育平台，以孩子学习成果为导向，历时8年，提供线上、线下家长与孩子共同学习成长的课程体系及平台，拥有课程开发及服务能力。\n", "520学习法是扶鹰教育推出的一门教授孩子如何主动学习的课程，可以帮助学生建立自信心，在课上主动举手发言，改善学生拖延的的问题，积极完成作业，自动自发学习，从而发现学习中的乐趣，提高学习成绩，还可以教会家长正确的教育方法，而不是遇到问题用打骂解决。\n", "扶鹰学霸二阶计划是一个旨在帮助孩子提高学习能力，培养良好的学习习惯和方法的课程。如今，许多家长都希望孩子能够在学业上取得优异的成绩，以便为将来的人生铺平道路。然而，孩子在成长过程中有着许多独特的特点和需求，因此，如何有效地帮助他们成为学霸，成为了许多家长和教育者关注的问题。\n", "孩子在成长过程中，不仅需要掌握知识，还需要培养良好的学习习惯和方法。扶鹰学霸二阶计划正是针对这一需求，为学生提供了一系列实用的学习方法和技巧。通过这个计划，孩子可以了解到如何有效地进行时间管理，如何提高记忆力和理解力，以及如何进行有效的复习和总结。这些方法和技巧不仅可以帮助孩子在学业上取得优异的成绩，还可以使他们受益终身。\n", "扶鹰学霸二阶计划还注重培养孩子的独立思考能力和创新能力。在现代社会，这些能力对于一个人的发展至关重要。通过这个计划，孩子可以学习到如何独立地思考问题，如何寻找问题的解决办法，以及如何进行创新和发明。这些能力和品质不仅可以帮助孩子在学业上取得优异的成绩，还可以使他们在未来的工作和生活中取得成功。\n", "成为学霸并不是一件容易的事情，它需要孩子具备许多品质和能力。扶鹰学霸二阶计划可以帮助孩子掌握这些品质和能力，使他们在学习过程中更加自信和从容。通过这个计划，孩子可以了解到如何有效地进行学习，如何处理学习中的困难和挫折，以及如何保持积极的心态和乐观的情绪。这些品质和能力不仅可以帮助孩子在学业上取得优异的成绩，还可以使他们在未来的工作和生活中取得成功。\n", "总的来说，扶鹰学霸二阶计划对于培养孩子成为学霸有着重要的作用。它可以帮助孩子掌握有效的学习方法和技巧，培养良好的学习习惯和品质，以及培养独立思考能力和创新能力。\n", "\n"]}], "source": ["import os\n", "from dotenv import load_dotenv, find_dotenv\n", "\n", "load_dotenv()\n", "\n", "rag = MyRAG()\n", "query = \"谁是王金海\"\n", "res = rag.query(query)\n", "print(res)"]}, {"cell_type": "code", "execution_count": null, "id": "bdb39f96-fc32-4250-9869-63ee43944a5c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "29eac642-0999-44e2-b291-0303beb1a1c3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b7984d04-ba6a-45a1-9366-f3e4bf8defff", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "16d7f666-65a3-4f6d-9eaf-9b25fe200e4e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["<>:2: <PERSON><PERSON>tax<PERSON>arning: invalid escape sequence '\\9'\n", "<>:2: <PERSON><PERSON>tax<PERSON>arning: invalid escape sequence '\\9'\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_15992\\1202595396.py:2: SyntaxWarning: invalid escape sequence '\\9'\n", "  reader = PdfReader('C:\\扶鹰资料\\扶小鹰绘本资料\\9781869614744_sportsonwheels_txt_aus_hr.pdf')\n"]}], "source": ["from PyPDF2 import PdfReader, PdfWriter\n", "reader = PdfReader('C:\\扶鹰资料\\扶小鹰绘本资料\\9781869614744_sportsonwheels_txt_aus_hr.pdf')"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["33"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["len(reader.pages)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["writer = PdfWriter()\n", "for i in range(17, 33):\n", "    writer.add_page(reader.pages[i])"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["with open('C:/扶鹰资料/扶小鹰绘本资料/sportsonwheels2.pdf', \"wb\") as out_file:\n", "    writer.write(out_file)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 2}
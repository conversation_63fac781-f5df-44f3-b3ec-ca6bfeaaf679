{"cells": [{"cell_type": "code", "execution_count": 2, "id": "e5d18e0b-ab89-4f2f-9819-4da30deabcb7", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["from dotenv import load_dotenv\n", "\n", "import tiktoken\n", "import os\n", "\n", "from graphrag.query.structured_search.global_search.search import GlobalSearch\n", "from graphrag.query.structured_search.local_search.search import LocalSearch\n", "from graphrag.query.structured_search.drift_search.search import DRIFTSearch\n", "from graphrag.query.structured_search.global_search.community_context import GlobalCommunityContext\n", "from graphrag.query.structured_search.local_search.mixed_context import LocalSearchMixedContext\n", "from graphrag.query.structured_search.drift_search.drift_context import DRIFTSearchContextBuilder\n", "from graphrag.query.context_builder.entity_extraction import EntityVectorStoreKey\n", "from graphrag.vector_stores.lancedb import LanceDBVectorStore\n", "from graphrag.config.models.drift_search_config import DRIFTSearchConfig\n", "from graphrag.query.indexer_adapters import (\n", "    read_indexer_covariates,\n", "    read_indexer_entities,\n", "    read_indexer_relationships,\n", "    read_indexer_report_embeddings,\n", "    read_indexer_reports,\n", "    read_indexer_text_units,\n", "    read_indexer_communities\n", ")\n", "from graphrag.query.llm.oai.embedding import OpenAIEmbedding\n", "from graphrag.query.llm.oai.typing import OpenaiApiType\n", "import pandas as pd\n", "\n", "\n", "load_dotenv()"]}, {"cell_type": "code", "execution_count": 3, "id": "b7696fdc-5d31-4b87-8ea0-b9e55a2465b8", "metadata": {}, "outputs": [], "source": ["embedding_model = \"text-embedding-v3\"\n", "text_embedder = OpenAIEmbedding(\n", "    api_key=os.getenv('DASHSCOPE_API_KEY'),\n", "    api_base=os.getenv('DASHSCOPE_API_URL'),\n", "    api_type=OpenaiApiType.OpenAI,\n", "    model=embedding_model,\n", "    deployment_name=embedding_model,\n", "    max_retries=20,\n", ")"]}, {"cell_type": "code", "execution_count": 4, "id": "2cbd59b3-ff30-432d-a8ad-e66b683f8f6c", "metadata": {}, "outputs": [], "source": ["\n", "INPUT_DIR = \"C:/workspace/graph-rag/test2/output\"\n", "LANCEDB_URI = f\"{INPUT_DIR}/lancedb\"\n", "COMMUNITY_LEVEL = 2\n", "CLAIM_EXTRACTION_ENABLED = False\n", "RESPONSE_TYPE = \"single paragraph\"\n", "\n", "COMMUNITY_TABLE = \"create_final_communities\"\n", "COMMUNITY_REPORT_TABLE = \"create_final_community_reports\"\n", "ENTITY_TABLE = \"create_final_nodes\"\n", "ENTITY_EMBEDDING_TABLE = \"create_final_entities\"\n", "RELATIONSHIP_TABLE = \"create_final_relationships\"\n", "COVARIATE_TABLE = \"create_final_covariates\"\n", "TEXT_UNIT_TABLE = \"create_final_text_units\"\n", "\n", "entity_df = pd.read_parquet(f\"{INPUT_DIR}/{ENTITY_TABLE}.parquet\")\n", "entity_embedding_df = pd.read_parquet(f\"{INPUT_DIR}/{ENTITY_EMBEDDING_TABLE}.parquet\")\n", "report_df = pd.read_parquet(f\"{INPUT_DIR}/{COMMUNITY_REPORT_TABLE}.parquet\")\n", "relationship_df = pd.read_parquet(f\"{INPUT_DIR}/{RELATIONSHIP_TABLE}.parquet\")    \n", "#covariate_df = pd.read_parquet(f\"{INPUT_DIR}/{COVARIATE_TABLE}.parquet\") if claim_extraction_enabled else pd.DataFrame()\n", "text_unit_df = pd.read_parquet(f\"{INPUT_DIR}/{TEXT_UNIT_TABLE}.parquet\")\n", "community_df = pd.read_parquet(f\"{INPUT_DIR}/{COMMUNITY_TABLE}.parquet\")\n", "\n", "entities = read_indexer_entities(entity_df, entity_embedding_df, COMMUNITY_LEVEL)\n", "relationships = read_indexer_relationships(relationship_df)\n", "claims = read_indexer_covariates(covariate_df) if CLAIM_EXTRACTION_ENABLED else []\n", "reports = read_indexer_reports(report_df, entity_df, COMMUNITY_LEVEL)\n", "text_units = read_indexer_text_units(text_unit_df)\n", "communities = read_indexer_communities(community_df, entity_df, report_df)\n", "\n", "description_embedding_store = LanceDBVectorStore(\n", "    collection_name=\"default-entity-description\",\n", ")\n", "description_embedding_store.connect(db_uri=LANCEDB_URI)\n", "\n", "full_content_embedding_store = LanceDBVectorStore(\n", "    collection_name=\"default-community-full_content\",\n", ")\n", "full_content_embedding_store.connect(db_uri=LANCEDB_URI)"]}, {"cell_type": "code", "execution_count": 5, "id": "239bab66-571c-412d-806f-b7dfbf21db92", "metadata": {}, "outputs": [], "source": ["\n", "token_encoder = tiktoken.get_encoding(\"cl100k_base\")\n", "context_builder = LocalSearchMixedContext(\n", "        community_reports=reports,\n", "        text_units=text_units,\n", "        entities=entities,\n", "        relationships=relationships,\n", "        #covariates={\"claims\": claims} if CLAIM_EXTRACTION_ENABLED else None,\n", "        entity_text_embeddings=description_embedding_store,\n", "        embedding_vectorstore_key=EntityVectorStoreKey.ID,\n", "        text_embedder=text_embedder,\n", "        token_encoder=token_encoder,\n", "    )"]}, {"cell_type": "code", "execution_count": 6, "id": "ae354f79-c666-4231-ba7e-666b85bc911f", "metadata": {}, "outputs": [], "source": ["context_builder_params={\n", "    \"text_unit_prop\": 0.5,\n", "    \"community_prop\": 0.1,\n", "    \"conversation_history_max_turns\": 5,\n", "    \"conversation_history_user_turns_only\": True,\n", "    \"top_k_mapped_entities\": 10,\n", "    \"top_k_relationships\": 10,\n", "    \"include_entity_rank\": True,\n", "    \"include_relationship_weight\": True,\n", "    \"include_community_rank\": <PERSON><PERSON><PERSON>,\n", "    \"return_candidate_context\": <PERSON><PERSON><PERSON>,\n", "    \"embedding_vectorstore_key\": EntityVectorStoreKey.ID,\n", "    \"max_tokens\": 12_000,\n", "}"]}, {"cell_type": "code", "execution_count": 8, "id": "ce554e2b-3a0d-48bb-868d-c51bbeea7ec6", "metadata": {}, "outputs": [], "source": ["result = context_builder.build_context(\n", "    query=\"父母总是唠叨怎么办？\",\n", "    #conversation_history=conversation_history,\n", "    **context_builder_params,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1820b45c-60d8-4ef0-b247-463f984ad0e7", "metadata": {}, "outputs": [], "source": ["result"]}, {"cell_type": "code", "execution_count": null, "id": "2fd3301c-5083-439b-9742-6ca34c7f9510", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "code", "execution_count": 1, "id": "7d4a1502-5d9a-4837-8062-e043962a0743", "metadata": {}, "outputs": [], "source": ["from langgraph.graph import MessagesState\n", "\n", "class State(MessagesState):\n", "   documents: list[str]  # 扩展其他字段（可选）"]}, {"cell_type": "code", "execution_count": 2, "id": "f8ed7500-1826-471c-bf9b-94ac2523812e", "metadata": {}, "outputs": [], "source": ["import os\n", "from dotenv import load_dotenv, find_dotenv\n", "\n", "load_dotenv()\n", "from langchain_openai import ChatOpenAI\n", "import os\n", "\n", "llm = ChatOpenAI(\n", "    api_key=os.getenv('MODAL_API_KEY'),\n", "    base_url=os.getenv('MODAL_URL'),\n", "    model=os.getenv('MODAL_NAME'),\n", "    # other params...\n", ")"]}, {"cell_type": "code", "execution_count": 5, "id": "c66a1369-d825-40f4-a18f-a9e3d2ab704b", "metadata": {}, "outputs": [], "source": ["from typing import Literal\n", "from langchain_openai import ChatOpenAI\n", "from langgraph.graph import StateGraph, MessagesState, START, END\n", "from langgraph.types import Command\n", "\n", "def supervisor(state: MessagesState) -> Command[Literal[\"agent_1\", \"agent_2\", END]]:\n", "    # you can pass relevant parts of the state to the LLM (e.g., state[\"messages\"])\n", "    # to determine which agent to call next. a common pattern is to call the model\n", "    # with a structured output (e.g. force it to return an output with a \"next_agent\" field)\n", "    response = model.invoke(...)\n", "    # route to one of the agents or exit based on the supervisor's decision\n", "    # if the supervisor returns \"__end__\", the graph will finish execution\n", "    return Command(goto=response[\"next_agent\"])\n", "\n", "def agent_1(state: MessagesState) -> Command[Literal[\"supervisor\"]]:\n", "    # you can pass relevant parts of the state to the LLM (e.g., state[\"messages\"])\n", "    # and add any additional logic (different models, custom prompts, structured output, etc.)\n", "    response = model.invoke(...)\n", "    return Command(\n", "        goto=\"supervisor\",\n", "        update={\"messages\": [response]},\n", "    )\n", "\n", "def agent_2(state: MessagesState) -> Command[Literal[\"supervisor\"]]:\n", "    response = model.invoke(...)\n", "    return Command(\n", "        goto=\"supervisor\",\n", "        update={\"messages\": [response]},\n", "    )\n", "\n", "builder = StateGraph(MessagesState)\n", "builder.add_node(supervisor)\n", "builder.add_node(agent_1)\n", "builder.add_node(agent_2)\n", "\n", "builder.add_edge(START, \"supervisor\")\n", "\n", "supervisor = builder.compile()"]}, {"cell_type": "code", "execution_count": 6, "id": "bd2493bd-4c33-43da-9c49-3cccfb1cf942", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import Image, display\n", "\n", "display(Image(supervisor.get_graph(xray=True).draw_mermaid_png()))"]}, {"cell_type": "code", "execution_count": null, "id": "c22f77a5-9c26-4dd5-b2c1-261c3da8dc35", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 8, "id": "ead613bb-0a8c-410b-9557-cb440ec1399d", "metadata": {}, "outputs": [], "source": ["\n", "from langchain_core.messages import HumanMessage, AIMessage\n", "\n", "# 节点1: 接收用户输入\n", "def receive_input(state: State):\n", "   user_input = input(\"User: \")\n", "   return {\"messages\": [HumanMessage(content=user_input)]}\n", "\n", "def generate_response(state: State):\n", "    last_message = state[\"messages\"][-1]\n", "    response = llm.invoke(last_message.content)\n", "    print(response)\n", "    return {\"messages\": [AIMessage(content=response)]}\n", "\n", "def save_history(state: State):\n", "   return {\"messages\": []} \n", "    \n", "def call_tool(state: State):\n", "   last_message = state[\"messages\"][-1]\n", "   return {\"messages\": [ToolMessage(content=\"1111\")]}"]}, {"cell_type": "code", "execution_count": 12, "id": "6659fa43-de7f-4fe1-b608-d201102b1404", "metadata": {}, "outputs": [], "source": ["from langgraph.graph import StateGraph, START, END\n", "\n", "builder = StateGraph(State)\n", "builder.add_node(\"receive_input\", receive_input)\n", "builder.add_node(\"generate_response\", generate_response)\n", "#builder.add_node(\"save_history\", save_history)\n", "builder.add_node(\"call_tool\", call_tool)\n", "\n", "\n", "# 设置边的流转逻辑\n", "builder.add_edge(START, \"receive_input\")\n", "builder.add_edge(\"receive_input\", \"generate_response\")\n", "builder.add_conditional_edges(\n", "   \"generate_response\",\n", "   lambda state: \"tools\" if \"search\" in state[\"messages\"][-1].content else END,\n", "   {\"tools\": \"call_tool\", END: END}\n", ")\n", "builder.add_edge(\"call_tool\", END)\n", "# builder.add_edge(\"save_history\", END)\n", "\n", "graph = builder.compile()"]}, {"cell_type": "code", "execution_count": 13, "id": "cef684ae-9ce8-4d3e-b306-b51e2757ff63", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import Image, display\n", "\n", "display(Image(graph.get_graph(xray=True).draw_mermaid_png()))"]}, {"cell_type": "code", "execution_count": null, "id": "af60202b-d8b3-472d-b4db-4f42f89ca8f3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 11, "id": "abc2d149-c2fe-47f1-8e89-ac9dafc0ce79", "metadata": {}, "outputs": [{"name": "stdin", "output_type": "stream", "text": ["User:  你好\n"]}, {"name": "stdout", "output_type": "stream", "text": ["节点输出: {'receive_input': {'messages': [HumanMessage(content='你好', additional_kwargs={}, response_metadata={}, id='95223ad7-cbc4-4f22-8e27-ed8b27018cfd')]}}\n"]}, {"ename": "ValidationError", "evalue": "21 validation errors for AIMessage\ncontent.str\n  Input should be a valid string [type=string_type, input_value=AIMessage(content='你好...put_token_details': {}}), input_type=AIMessage]\n    For further information visit https://errors.pydantic.dev/2.10/v/string_type\ncontent.list[union[str,dict[any,any]]].0.str\n  Input should be a valid string [type=string_type, input_value=('content', '你好！很...可以帮忙的吗？'), input_type=tuple]\n    For further information visit https://errors.pydantic.dev/2.10/v/string_type\ncontent.list[union[str,dict[any,any]]].0.dict[any,any]\n  Input should be a valid dictionary [type=dict_type, input_value=('content', '你好！很...可以帮忙的吗？'), input_type=tuple]\n    For further information visit https://errors.pydantic.dev/2.10/v/dict_type\ncontent.list[union[str,dict[any,any]]].1.str\n  Input should be a valid string [type=string_type, input_value=('additional_kwargs', {'refusal': None}), input_type=tuple]\n    For further information visit https://errors.pydantic.dev/2.10/v/string_type\ncontent.list[union[str,dict[any,any]]].1.dict[any,any]\n  Input should be a valid dictionary [type=dict_type, input_value=('additional_kwargs', {'refusal': None}), input_type=tuple]\n    For further information visit https://errors.pydantic.dev/2.10/v/dict_type\ncontent.list[union[str,dict[any,any]]].2.str\n  Input should be a valid string [type=string_type, input_value=('response_metadata', {'t...top', 'logprobs': None}), input_type=tuple]\n    For further information visit https://errors.pydantic.dev/2.10/v/string_type\ncontent.list[union[str,dict[any,any]]].2.dict[any,any]\n  Input should be a valid dictionary [type=dict_type, input_value=('response_metadata', {'t...top', 'logprobs': None}), input_type=tuple]\n    For further information visit https://errors.pydantic.dev/2.10/v/dict_type\ncontent.list[union[str,dict[any,any]]].3.str\n  Input should be a valid string [type=string_type, input_value=('type', 'ai'), input_type=tuple]\n    For further information visit https://errors.pydantic.dev/2.10/v/string_type\ncontent.list[union[str,dict[any,any]]].3.dict[any,any]\n  Input should be a valid dictionary [type=dict_type, input_value=('type', 'ai'), input_type=tuple]\n    For further information visit https://errors.pydantic.dev/2.10/v/dict_type\ncontent.list[union[str,dict[any,any]]].4.str\n  Input should be a valid string [type=string_type, input_value=('name', None), input_type=tuple]\n    For further information visit https://errors.pydantic.dev/2.10/v/string_type\ncontent.list[union[str,dict[any,any]]].4.dict[any,any]\n  Input should be a valid dictionary [type=dict_type, input_value=('name', None), input_type=tuple]\n    For further information visit https://errors.pydantic.dev/2.10/v/dict_type\ncontent.list[union[str,dict[any,any]]].5.str\n  Input should be a valid string [type=string_type, input_value=('id', 'run-0016335a-0818...6a-80d7-c6f7c03152e6-0'), input_type=tuple]\n    For further information visit https://errors.pydantic.dev/2.10/v/string_type\ncontent.list[union[str,dict[any,any]]].5.dict[any,any]\n  Input should be a valid dictionary [type=dict_type, input_value=('id', 'run-0016335a-0818...6a-80d7-c6f7c03152e6-0'), input_type=tuple]\n    For further information visit https://errors.pydantic.dev/2.10/v/dict_type\ncontent.list[union[str,dict[any,any]]].6.str\n  Input should be a valid string [type=string_type, input_value=('example', False), input_type=tuple]\n    For further information visit https://errors.pydantic.dev/2.10/v/string_type\ncontent.list[union[str,dict[any,any]]].6.dict[any,any]\n  Input should be a valid dictionary [type=dict_type, input_value=('example', False), input_type=tuple]\n    For further information visit https://errors.pydantic.dev/2.10/v/dict_type\ncontent.list[union[str,dict[any,any]]].7.str\n  Input should be a valid string [type=string_type, input_value=('tool_calls', []), input_type=tuple]\n    For further information visit https://errors.pydantic.dev/2.10/v/string_type\ncontent.list[union[str,dict[any,any]]].7.dict[any,any]\n  Input should be a valid dictionary [type=dict_type, input_value=('tool_calls', []), input_type=tuple]\n    For further information visit https://errors.pydantic.dev/2.10/v/dict_type\ncontent.list[union[str,dict[any,any]]].8.str\n  Input should be a valid string [type=string_type, input_value=('invalid_tool_calls', []), input_type=tuple]\n    For further information visit https://errors.pydantic.dev/2.10/v/string_type\ncontent.list[union[str,dict[any,any]]].8.dict[any,any]\n  Input should be a valid dictionary [type=dict_type, input_value=('invalid_tool_calls', []), input_type=tuple]\n    For further information visit https://errors.pydantic.dev/2.10/v/dict_type\ncontent.list[union[str,dict[any,any]]].9.str\n  Input should be a valid string [type=string_type, input_value=('usage_metadata', {'inpu...put_token_details': {}}), input_type=tuple]\n    For further information visit https://errors.pydantic.dev/2.10/v/string_type\ncontent.list[union[str,dict[any,any]]].9.dict[any,any]\n  Input should be a valid dictionary [type=dict_type, input_value=('usage_metadata', {'inpu...put_token_details': {}}), input_type=tuple]\n    For further information visit https://errors.pydantic.dev/2.10/v/dict_type", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mValidationError\u001b[0m                           <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[11], line 3\u001b[0m\n\u001b[0;32m      1\u001b[0m inputs \u001b[38;5;241m=\u001b[39m {\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmessages\u001b[39m\u001b[38;5;124m\"\u001b[39m: [\n\u001b[0;32m      2\u001b[0m ]}  \u001b[38;5;66;03m# 初始状态\u001b[39;00m\n\u001b[1;32m----> 3\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m step \u001b[38;5;129;01min\u001b[39;00m graph\u001b[38;5;241m.\u001b[39mstream(inputs):\n\u001b[0;32m      4\u001b[0m    \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m节点输出: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mstep\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[1;32mC:\\software\\python\\Lib\\site-packages\\langgraph\\pregel\\__init__.py:1724\u001b[0m, in \u001b[0;36mPregel.stream\u001b[1;34m(self, input, config, stream_mode, output_keys, interrupt_before, interrupt_after, debug, subgraphs)\u001b[0m\n\u001b[0;32m   1718\u001b[0m     \u001b[38;5;66;03m# Similarly to Bulk Synchronous Parallel / Pregel model\u001b[39;00m\n\u001b[0;32m   1719\u001b[0m     \u001b[38;5;66;03m# computation proceeds in steps, while there are channel updates.\u001b[39;00m\n\u001b[0;32m   1720\u001b[0m     \u001b[38;5;66;03m# Channel updates from step N are only visible in step N+1\u001b[39;00m\n\u001b[0;32m   1721\u001b[0m     \u001b[38;5;66;03m# channels are guaranteed to be immutable for the duration of the step,\u001b[39;00m\n\u001b[0;32m   1722\u001b[0m     \u001b[38;5;66;03m# with channel updates applied only at the transition between steps.\u001b[39;00m\n\u001b[0;32m   1723\u001b[0m     \u001b[38;5;28;01mwhile\u001b[39;00m loop\u001b[38;5;241m.\u001b[39mtick(input_keys\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39minput_channels):\n\u001b[1;32m-> 1724\u001b[0m         \u001b[38;5;28;01mfor\u001b[39;00m _ \u001b[38;5;129;01min\u001b[39;00m runner\u001b[38;5;241m.\u001b[39mtick(\n\u001b[0;32m   1725\u001b[0m             loop\u001b[38;5;241m.\u001b[39mtasks\u001b[38;5;241m.\u001b[39mvalues(),\n\u001b[0;32m   1726\u001b[0m             timeout\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstep_timeout,\n\u001b[0;32m   1727\u001b[0m             retry_policy\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mretry_policy,\n\u001b[0;32m   1728\u001b[0m             get_waiter\u001b[38;5;241m=\u001b[39mget_waiter,\n\u001b[0;32m   1729\u001b[0m         ):\n\u001b[0;32m   1730\u001b[0m             \u001b[38;5;66;03m# emit output\u001b[39;00m\n\u001b[0;32m   1731\u001b[0m             \u001b[38;5;28;01myield from\u001b[39;00m output()\n\u001b[0;32m   1732\u001b[0m \u001b[38;5;66;03m# emit output\u001b[39;00m\n", "File \u001b[1;32mC:\\software\\python\\Lib\\site-packages\\langgraph\\pregel\\runner.py:230\u001b[0m, in \u001b[0;36mPregelRunner.tick\u001b[1;34m(self, tasks, reraise, timeout, retry_policy, get_waiter)\u001b[0m\n\u001b[0;32m    228\u001b[0m t \u001b[38;5;241m=\u001b[39m tasks[\u001b[38;5;241m0\u001b[39m]\n\u001b[0;32m    229\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m--> 230\u001b[0m     \u001b[43mrun_with_retry\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    231\u001b[0m \u001b[43m        \u001b[49m\u001b[43mt\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    232\u001b[0m \u001b[43m        \u001b[49m\u001b[43mretry_policy\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    233\u001b[0m \u001b[43m        \u001b[49m\u001b[43mconfigurable\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m{\u001b[49m\n\u001b[0;32m    234\u001b[0m \u001b[43m            \u001b[49m\u001b[43mCONFIG_KEY_SEND\u001b[49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mpartial\u001b[49m\u001b[43m(\u001b[49m\u001b[43mwriter\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mt\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    235\u001b[0m \u001b[43m            \u001b[49m\u001b[43mCONFIG_KEY_CALL\u001b[49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mpartial\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcall\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mt\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    236\u001b[0m \u001b[43m        \u001b[49m\u001b[43m}\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    237\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    238\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcommit(t, \u001b[38;5;28;01mNone\u001b[39;00m)\n\u001b[0;32m    239\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m exc:\n", "File \u001b[1;32mC:\\software\\python\\Lib\\site-packages\\langgraph\\pregel\\retry.py:40\u001b[0m, in \u001b[0;36mrun_with_retry\u001b[1;34m(task, retry_policy, configurable)\u001b[0m\n\u001b[0;32m     38\u001b[0m     task\u001b[38;5;241m.\u001b[39mwrites\u001b[38;5;241m.\u001b[39mclear()\n\u001b[0;32m     39\u001b[0m     \u001b[38;5;66;03m# run the task\u001b[39;00m\n\u001b[1;32m---> 40\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mtask\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mproc\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43minvoke\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtask\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43minput\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     41\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m ParentCommand \u001b[38;5;28;01mas\u001b[39;00m exc:\n\u001b[0;32m     42\u001b[0m     ns: \u001b[38;5;28mstr\u001b[39m \u001b[38;5;241m=\u001b[39m config[CONF][CONFIG_KEY_CHECKPOINT_NS]\n", "File \u001b[1;32mC:\\software\\python\\Lib\\site-packages\\langgraph\\utils\\runnable.py:506\u001b[0m, in \u001b[0;36mRunnableSeq.invoke\u001b[1;34m(self, input, config, **kwargs)\u001b[0m\n\u001b[0;32m    502\u001b[0m config \u001b[38;5;241m=\u001b[39m patch_config(\n\u001b[0;32m    503\u001b[0m     config, callbacks\u001b[38;5;241m=\u001b[39mrun_manager\u001b[38;5;241m.\u001b[39mget_child(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mseq:step:\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mi\u001b[38;5;250m \u001b[39m\u001b[38;5;241m+\u001b[39m\u001b[38;5;250m \u001b[39m\u001b[38;5;241m1\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m    504\u001b[0m )\n\u001b[0;32m    505\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m i \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m0\u001b[39m:\n\u001b[1;32m--> 506\u001b[0m     \u001b[38;5;28minput\u001b[39m \u001b[38;5;241m=\u001b[39m \u001b[43mstep\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43minvoke\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43minput\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    507\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m    508\u001b[0m     \u001b[38;5;28minput\u001b[39m \u001b[38;5;241m=\u001b[39m step\u001b[38;5;241m.\u001b[39minvoke(\u001b[38;5;28minput\u001b[39m, config)\n", "File \u001b[1;32mC:\\software\\python\\Lib\\site-packages\\langgraph\\utils\\runnable.py:270\u001b[0m, in \u001b[0;36mRunnableCallable.invoke\u001b[1;34m(self, input, config, **kwargs)\u001b[0m\n\u001b[0;32m    268\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m    269\u001b[0m     context\u001b[38;5;241m.\u001b[39mrun(_set_config_context, config)\n\u001b[1;32m--> 270\u001b[0m     ret \u001b[38;5;241m=\u001b[39m \u001b[43mcontext\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrun\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfunc\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    271\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(ret, Runnable) \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mrecurse:\n\u001b[0;32m    272\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m ret\u001b[38;5;241m.\u001b[39minvoke(\u001b[38;5;28minput\u001b[39m, config)\n", "Cell \u001b[1;32mIn[8], line 11\u001b[0m, in \u001b[0;36mgenerate_response\u001b[1;34m(state)\u001b[0m\n\u001b[0;32m      9\u001b[0m last_message \u001b[38;5;241m=\u001b[39m state[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmessages\u001b[39m\u001b[38;5;124m\"\u001b[39m][\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m]\n\u001b[0;32m     10\u001b[0m response \u001b[38;5;241m=\u001b[39m llm\u001b[38;5;241m.\u001b[39minvoke(last_message\u001b[38;5;241m.\u001b[39mcontent)\n\u001b[1;32m---> 11\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m {\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmessages\u001b[39m\u001b[38;5;124m\"\u001b[39m: [\u001b[43mAIMessage\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcontent\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mresponse\u001b[49m\u001b[43m)\u001b[49m]}\n", "File \u001b[1;32mC:\\software\\python\\Lib\\site-packages\\langchain_core\\messages\\ai.py:179\u001b[0m, in \u001b[0;36mAIMessage.__init__\u001b[1;34m(self, content, **kwargs)\u001b[0m\n\u001b[0;32m    170\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m__init__\u001b[39m(\n\u001b[0;32m    171\u001b[0m     \u001b[38;5;28mself\u001b[39m, content: Union[\u001b[38;5;28mstr\u001b[39m, \u001b[38;5;28mlist\u001b[39m[Union[\u001b[38;5;28mstr\u001b[39m, \u001b[38;5;28mdict\u001b[39m]]], \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs: Any\n\u001b[0;32m    172\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[0;32m    173\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Pass in content as positional arg.\u001b[39;00m\n\u001b[0;32m    174\u001b[0m \n\u001b[0;32m    175\u001b[0m \u001b[38;5;124;03m    Args:\u001b[39;00m\n\u001b[0;32m    176\u001b[0m \u001b[38;5;124;03m        content: The content of the message.\u001b[39;00m\n\u001b[0;32m    177\u001b[0m \u001b[38;5;124;03m        kwargs: Additional arguments to pass to the parent class.\u001b[39;00m\n\u001b[0;32m    178\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[1;32m--> 179\u001b[0m     \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[38;5;21;43m__init__\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mcontent\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcontent\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mC:\\software\\python\\Lib\\site-packages\\langchain_core\\messages\\base.py:76\u001b[0m, in \u001b[0;36mBaseMessage.__init__\u001b[1;34m(self, content, **kwargs)\u001b[0m\n\u001b[0;32m     67\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m__init__\u001b[39m(\n\u001b[0;32m     68\u001b[0m     \u001b[38;5;28mself\u001b[39m, content: Union[\u001b[38;5;28mstr\u001b[39m, \u001b[38;5;28mlist\u001b[39m[Union[\u001b[38;5;28mstr\u001b[39m, \u001b[38;5;28mdict\u001b[39m]]], \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs: Any\n\u001b[0;32m     69\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[0;32m     70\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Pass in content as positional arg.\u001b[39;00m\n\u001b[0;32m     71\u001b[0m \n\u001b[0;32m     72\u001b[0m \u001b[38;5;124;03m    Args:\u001b[39;00m\n\u001b[0;32m     73\u001b[0m \u001b[38;5;124;03m        content: The string contents of the message.\u001b[39;00m\n\u001b[0;32m     74\u001b[0m \u001b[38;5;124;03m        kwargs: Additional fields to pass to the\u001b[39;00m\n\u001b[0;32m     75\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[1;32m---> 76\u001b[0m     \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[38;5;21;43m__init__\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mcontent\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcontent\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mC:\\software\\python\\Lib\\site-packages\\langchain_core\\load\\serializable.py:125\u001b[0m, in \u001b[0;36mSerializable.__init__\u001b[1;34m(self, *args, **kwargs)\u001b[0m\n\u001b[0;32m    123\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m__init__\u001b[39m(\u001b[38;5;28mself\u001b[39m, \u001b[38;5;241m*\u001b[39margs: Any, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs: Any) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[0;32m    124\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"\"\"\"\u001b[39;00m\n\u001b[1;32m--> 125\u001b[0m     \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[38;5;21;43m__init__\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mC:\\software\\python\\Lib\\site-packages\\pydantic\\main.py:214\u001b[0m, in \u001b[0;36mBaseModel.__init__\u001b[1;34m(self, **data)\u001b[0m\n\u001b[0;32m    212\u001b[0m \u001b[38;5;66;03m# `__tracebackhide__` tells pytest and some other tools to omit this function from tracebacks\u001b[39;00m\n\u001b[0;32m    213\u001b[0m __tracebackhide__ \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[1;32m--> 214\u001b[0m validated_self \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m__pydantic_validator__\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mvalidate_python\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdata\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mself_instance\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[0;32m    215\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m validated_self:\n\u001b[0;32m    216\u001b[0m     warnings\u001b[38;5;241m.\u001b[39mwarn(\n\u001b[0;32m    217\u001b[0m         \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mA custom validator is returning a value other than `self`.\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m'\u001b[39m\n\u001b[0;32m    218\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mReturning anything other than `self` from a top level model validator isn\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mt supported when validating via `__init__`.\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    219\u001b[0m         \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mSee the `model_validator` docs (https://docs.pydantic.dev/latest/concepts/validators/#model-validators) for more details.\u001b[39m\u001b[38;5;124m'\u001b[39m,\n\u001b[0;32m    220\u001b[0m         stacklevel\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m2\u001b[39m,\n\u001b[0;32m    221\u001b[0m     )\n", "\u001b[1;31mValidationError\u001b[0m: 21 validation errors for AIMessage\ncontent.str\n  Input should be a valid string [type=string_type, input_value=AIMessage(content='你好...put_token_details': {}}), input_type=AIMessage]\n    For further information visit https://errors.pydantic.dev/2.10/v/string_type\ncontent.list[union[str,dict[any,any]]].0.str\n  Input should be a valid string [type=string_type, input_value=('content', '你好！很...可以帮忙的吗？'), input_type=tuple]\n    For further information visit https://errors.pydantic.dev/2.10/v/string_type\ncontent.list[union[str,dict[any,any]]].0.dict[any,any]\n  Input should be a valid dictionary [type=dict_type, input_value=('content', '你好！很...可以帮忙的吗？'), input_type=tuple]\n    For further information visit https://errors.pydantic.dev/2.10/v/dict_type\ncontent.list[union[str,dict[any,any]]].1.str\n  Input should be a valid string [type=string_type, input_value=('additional_kwargs', {'refusal': None}), input_type=tuple]\n    For further information visit https://errors.pydantic.dev/2.10/v/string_type\ncontent.list[union[str,dict[any,any]]].1.dict[any,any]\n  Input should be a valid dictionary [type=dict_type, input_value=('additional_kwargs', {'refusal': None}), input_type=tuple]\n    For further information visit https://errors.pydantic.dev/2.10/v/dict_type\ncontent.list[union[str,dict[any,any]]].2.str\n  Input should be a valid string [type=string_type, input_value=('response_metadata', {'t...top', 'logprobs': None}), input_type=tuple]\n    For further information visit https://errors.pydantic.dev/2.10/v/string_type\ncontent.list[union[str,dict[any,any]]].2.dict[any,any]\n  Input should be a valid dictionary [type=dict_type, input_value=('response_metadata', {'t...top', 'logprobs': None}), input_type=tuple]\n    For further information visit https://errors.pydantic.dev/2.10/v/dict_type\ncontent.list[union[str,dict[any,any]]].3.str\n  Input should be a valid string [type=string_type, input_value=('type', 'ai'), input_type=tuple]\n    For further information visit https://errors.pydantic.dev/2.10/v/string_type\ncontent.list[union[str,dict[any,any]]].3.dict[any,any]\n  Input should be a valid dictionary [type=dict_type, input_value=('type', 'ai'), input_type=tuple]\n    For further information visit https://errors.pydantic.dev/2.10/v/dict_type\ncontent.list[union[str,dict[any,any]]].4.str\n  Input should be a valid string [type=string_type, input_value=('name', None), input_type=tuple]\n    For further information visit https://errors.pydantic.dev/2.10/v/string_type\ncontent.list[union[str,dict[any,any]]].4.dict[any,any]\n  Input should be a valid dictionary [type=dict_type, input_value=('name', None), input_type=tuple]\n    For further information visit https://errors.pydantic.dev/2.10/v/dict_type\ncontent.list[union[str,dict[any,any]]].5.str\n  Input should be a valid string [type=string_type, input_value=('id', 'run-0016335a-0818...6a-80d7-c6f7c03152e6-0'), input_type=tuple]\n    For further information visit https://errors.pydantic.dev/2.10/v/string_type\ncontent.list[union[str,dict[any,any]]].5.dict[any,any]\n  Input should be a valid dictionary [type=dict_type, input_value=('id', 'run-0016335a-0818...6a-80d7-c6f7c03152e6-0'), input_type=tuple]\n    For further information visit https://errors.pydantic.dev/2.10/v/dict_type\ncontent.list[union[str,dict[any,any]]].6.str\n  Input should be a valid string [type=string_type, input_value=('example', False), input_type=tuple]\n    For further information visit https://errors.pydantic.dev/2.10/v/string_type\ncontent.list[union[str,dict[any,any]]].6.dict[any,any]\n  Input should be a valid dictionary [type=dict_type, input_value=('example', False), input_type=tuple]\n    For further information visit https://errors.pydantic.dev/2.10/v/dict_type\ncontent.list[union[str,dict[any,any]]].7.str\n  Input should be a valid string [type=string_type, input_value=('tool_calls', []), input_type=tuple]\n    For further information visit https://errors.pydantic.dev/2.10/v/string_type\ncontent.list[union[str,dict[any,any]]].7.dict[any,any]\n  Input should be a valid dictionary [type=dict_type, input_value=('tool_calls', []), input_type=tuple]\n    For further information visit https://errors.pydantic.dev/2.10/v/dict_type\ncontent.list[union[str,dict[any,any]]].8.str\n  Input should be a valid string [type=string_type, input_value=('invalid_tool_calls', []), input_type=tuple]\n    For further information visit https://errors.pydantic.dev/2.10/v/string_type\ncontent.list[union[str,dict[any,any]]].8.dict[any,any]\n  Input should be a valid dictionary [type=dict_type, input_value=('invalid_tool_calls', []), input_type=tuple]\n    For further information visit https://errors.pydantic.dev/2.10/v/dict_type\ncontent.list[union[str,dict[any,any]]].9.str\n  Input should be a valid string [type=string_type, input_value=('usage_metadata', {'inpu...put_token_details': {}}), input_type=tuple]\n    For further information visit https://errors.pydantic.dev/2.10/v/string_type\ncontent.list[union[str,dict[any,any]]].9.dict[any,any]\n  Input should be a valid dictionary [type=dict_type, input_value=('usage_metadata', {'inpu...put_token_details': {}}), input_type=tuple]\n    For further information visit https://errors.pydantic.dev/2.10/v/dict_type", "\u001b[0mDuring task with name 'generate_response' and id 'f07e6be5-c0c5-4a14-04e3-c7ba65c6ecfa'"]}], "source": ["inputs = {\"messages\": [\n", "]}  # 初始状态\n", "for step in graph.stream(inputs):\n", "   print(f\"节点输出: {step}\")"]}, {"cell_type": "code", "execution_count": null, "id": "61672b5c-f37b-488b-872e-1c2218ce3e91", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "acf0569d-1f2d-4e16-84db-bd0597252a43", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5d266662-8838-4068-97ba-10c97ca21785", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 5}
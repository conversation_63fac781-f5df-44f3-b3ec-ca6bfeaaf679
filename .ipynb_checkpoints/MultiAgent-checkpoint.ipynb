{"cells": [{"cell_type": "code", "execution_count": 60, "id": "3320e496-645a-4a47-9f06-ab85577aa061", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "import pandas as pd\n", "import tiktoken\n", "\n", "from graphrag.query.context_builder.entity_extraction import EntityVectorStoreKey\n", "from graphrag.query.indexer_adapters import (\n", "    read_indexer_covariates,\n", "    read_indexer_entities,\n", "    read_indexer_relationships,\n", "    read_indexer_reports,\n", "    read_indexer_text_units,\n", ")\n", "from graphrag.query.llm.oai.chat_openai import ChatOpenAI\n", "from graphrag.query.llm.oai.embedding import OpenAIEmbedding\n", "from graphrag.query.llm.oai.typing import OpenaiApiType\n", "from graphrag.query.question_gen.local_gen import LocalQuestionGen\n", "from graphrag.query.structured_search.local_search.mixed_context import (\n", "    LocalSearchMixedContext,\n", ")\n", "from graphrag.query.structured_search.local_search.search import LocalSearch\n", "from graphrag.vector_stores.lancedb import LanceDBVectorStore\n", "    \n", "INPUT_DIR = \"C:/workspace/graph-rag/test1/output\"\n", "LANCEDB_URI = f\"{INPUT_DIR}/lancedb\"\n", "\n", "COMMUNITY_REPORT_TABLE = \"create_final_community_reports\"\n", "ENTITY_TABLE = \"create_final_nodes\"\n", "ENTITY_EMBEDDING_TABLE = \"create_final_entities\"\n", "COMMUNITY_TABLE = \"create_final_communities\"\n", "RELATIONSHIP_TABLE = \"create_final_relationships\"\n", "#COVARIATE_TABLE = \"create_final_covariates\"\n", "TEXT_UNIT_TABLE = \"create_final_text_units\"\n", "COMMUNITY_LEVEL = 2\n", "\n", "class MyRAG:\n", "    def __init__(self):\n", "        description_embedding_store = LanceDBVectorStore(\n", "            collection_name=\"default-entity-description\",\n", "        )\n", "        description_embedding_store.connect(db_uri=LANCEDB_URI)\n", "\n", "        entity_df = pd.read_parquet(f\"{INPUT_DIR}/{ENTITY_TABLE}.parquet\")\n", "        entity_embedding_df = pd.read_parquet(f\"{INPUT_DIR}/{ENTITY_EMBEDDING_TABLE}.parquet\")\n", "        entities = read_indexer_entities(entity_df, entity_embedding_df, COMMUNITY_LEVEL)\n", "\n", "        relationship_df = pd.read_parquet(f\"{INPUT_DIR}/{RELATIONSHIP_TABLE}.parquet\")\n", "        relationships = read_indexer_relationships(relationship_df)\n", "\n", "        report_df = pd.read_parquet(f\"{INPUT_DIR}/{COMMUNITY_REPORT_TABLE}.parquet\")\n", "        reports = read_indexer_reports(report_df, entity_df, COMMUNITY_LEVEL)\n", "\n", "        text_unit_df = pd.read_parquet(f\"{INPUT_DIR}/{TEXT_UNIT_TABLE}.parquet\")\n", "        text_units = read_indexer_text_units(text_unit_df)\n", "\n", "        embedding_model = \"text-embedding-v3\"\n", "        text_embedder = OpenAIEmbedding(\n", "            api_key=os.getenv('MODAL_API_KEY'),\n", "            api_base=os.getenv('MODAL_URL'),\n", "            api_type=OpenaiApiType.OpenAI,\n", "            model=embedding_model,\n", "            deployment_name=embedding_model,\n", "            max_retries=20,\n", "        )\n", "\n", "        token_encoder = tiktoken.get_encoding(\"cl100k_base\")\n", "\n", "        self.context_builder = LocalSearchMixedContext(\n", "            community_reports=reports,\n", "            text_units=text_units,\n", "            entities=entities,\n", "            relationships=relationships,\n", "            # if you did not run covariates during indexing, set this to None\n", "            #covariates=covariates,\n", "            entity_text_embeddings=description_embedding_store,\n", "            embedding_vectorstore_key=EntityVectorStoreKey.ID,  # if the vectorstore uses entity title as ids, set this to EntityVectorStoreKey.TITLE\n", "            text_embedder=text_embedder,\n", "            token_encoder=token_encoder,\n", "        )\n", "\n", "    def query(self, query: str): \n", "        context = self.context_builder.build_context(query)\n", "        return self.__parse_res(context.context_chunks)\n", "    \n", "    def __parse_res(self, res: str):\n", "        sections = res.split('\\n\\n\\n')  \n", "        source_content = \"\"\n", "        for section in sections:\n", "            if section.strip().startswith(\"-----Sources-----\"): \n", "                source_content = section\n", "                break\n", "        \n", "        content_arr = source_content.split('|')\n", "        content = content_arr[-1:]\n", "        return content[0]"]}, {"cell_type": "code", "execution_count": 35, "id": "640d0159-1d2e-4603-99a2-f005d217ef2e", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "from dotenv import load_dotenv, find_dotenv\n", "\n", "load_dotenv()"]}, {"cell_type": "code", "execution_count": 61, "id": "12c0e3e9-dfa8-4f41-b320-7b4590132623", "metadata": {}, "outputs": [], "source": ["from langchain.tools import BaseTool, StructuredTool, tool\n", "\n", "rag = MyRAG()\n", "\n", "@tool\n", "def introduce(text: str):\n", "    '''用于介绍公司和课程的基本信息,当询问扶鹰公司和520、二阶等课程的介绍时应该使用该工具'''\n", "    return rag.query(text)"]}, {"cell_type": "code", "execution_count": 54, "id": "74e95068-2d98-4652-bf01-d61393a56a05", "metadata": {}, "outputs": [], "source": ["from langchain_openai import ChatOpenAI\n", "import os\n", "\n", "llm = ChatOpenAI(\n", "    api_key=os.getenv('MODAL_API_KEY'),\n", "    base_url=os.getenv('MODAL_URL'),\n", "    model=os.getenv('MODAL_NAME'),\n", "    # other params...\n", ")"]}, {"cell_type": "code", "execution_count": 50, "id": "74f8fe16-1273-4321-a446-daf6a780df2b", "metadata": {}, "outputs": [], "source": ["# from langchain_community.chat_models.tongyi import ChatTongyi\n", "\n", "# llm = Chat<PERSON><PERSON>yi(api_key=os.getenv('MODAL_API_KEY'),model_name=os.getenv('MODAL_NAME'),temperature=0.7)\n", "# llm_with_tools = llm.bind_tools(tools)\n", "# llm_with_tools.invoke(\"扶鹰是做什么的\")"]}, {"cell_type": "code", "execution_count": 46, "id": "2a1db20d-6c20-4682-bcd6-51e0e108bc44", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\software\\python\\Lib\\site-packages\\langsmith\\client.py:241: LangSmithMissingAPIKeyWarning: API key must be provided when using hosted LangSmith API\n", "  warnings.warn(\n"]}], "source": ["from langchain import hub\n", "from langchain.agents import create_tool_calling_agent, AgentExecutor\n", "\n", "prompt = hub.pull(\"hwchase17/openai-functions-agent\")\n", "tools = [introduce]\n", "agent = create_tool_calling_agent(llm, tools, prompt)\n", "agent_executor = AgentExecutor(agent=agent, tools=tools)"]}, {"cell_type": "code", "execution_count": 55, "id": "22bd6b8b-2d75-4fb3-ac0e-7d3e6acd1987", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\software\\python\\Lib\\site-packages\\langsmith\\client.py:241: LangSmithMissingAPIKeyWarning: API key must be provided when using hosted LangSmith API\n", "  warnings.warn(\n"]}], "source": ["from langchain import hub\n", "from langchain.agents import create_react_agent, AgentExecutor\n", "\n", "prompt = hub.pull(\"hwchase17/react\")\n", "tools = [introduce]\n", "agent = create_react_agent(llm, tools, prompt)\n", "agent_executor = AgentExecutor(agent=agent, tools=tools, verbose=True)"]}, {"cell_type": "code", "execution_count": 49, "id": "d7643f83-c760-4e1c-9364-92d190227deb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new AgentExecutor chain...\u001b[0m\n", "\u001b[32;1m\u001b[1;3mI need to introduce the company 扶鹰 and its basic information. The appropriate action is to use the 'introduce' tool.\n", "\n", "Action: introduce  \n", "Action Input: 扶鹰公司介绍\u001b[0m\u001b[36;1m\u001b[1;3m杭州扶鹰教育科技有限公司是一家家庭教育企业，坐落于杭州上城区，在创始人王金海博士的带领下，致力于打造落地的家庭教育平台，以孩子学习成果为导向，历时8年，提供线上、线下家长与孩子共同学习成长的课程体系及平台，拥有课程开发及服务能力。\n", "520学习法是扶鹰教育推出的一门教授孩子如何主动学习的课程，可以帮助学生建立自信心，在课上主动举手发言，改善学生拖延的的问题，积极完成作业，自动自发学习，从而发现学习中的乐趣，提高学习成绩，还可以教会家长正确的教育方法，而不是遇到问题用打骂解决。\n", "扶鹰学霸二阶计划是一个旨在帮助孩子提高学习能力，培养良好的学习习惯和方法的课程。如今，许多家长都希望孩子能够在学业上取得优异的成绩，以便为将来的人生铺平道路。然而，孩子在成长过程中有着许多独特的特点和需求，因此，如何有效地帮助他们成为学霸，成为了许多家长和教育者关注的问题。\n", "孩子在成长过程中，不仅需要掌握知识，还需要培养良好的学习习惯和方法。扶鹰学霸二阶计划正是针对这一需求，为学生提供了一系列实用的学习方法和技巧。通过这个计划，孩子可以了解到如何有效地进行时间管理，如何提高记忆力和理解力，以及如何进行有效的复习和总结。这些方法和技巧不仅可以帮助孩子在学业上取得优异的成绩，还可以使他们受益终身。\n", "扶鹰学霸二阶计划还注重培养孩子的独立思考能力和创新能力。在现代社会，这些能力对于一个人的发展至关重要。通过这个计划，孩子可以学习到如何独立地思考问题，如何寻找问题的解决办法，以及如何进行创新和发明。这些能力和品质不仅可以帮助孩子在学业上取得优异的成绩，还可以使他们在未来的工作和生活中取得成功。\n", "成为学霸并不是一件容易的事情，它需要孩子具备许多品质和能力。扶鹰学霸二阶计划可以帮助孩子掌握这些品质和能力，使他们在学习过程中更加自信和从容。通过这个计划，孩子可以了解到如何有效地进行学习，如何处理学习中的困难和挫折，以及如何保持积极的心态和乐观的情绪。这些品质和能力不仅可以帮助孩子在学业上取得优异的成绩，还可以使他们在未来的工作和生活中取得成功。\n", "总的来说，扶鹰学霸二阶计划对于培养孩子成为学霸有着重要的作用。它可以帮助孩子掌握有效的学习方法和技巧，培养良好的学习习惯和品质，以及培养独立思考能力和创新能力。\n", "\u001b[0m\u001b[32;1m\u001b[1;3mFinal Answer: 扶鹰是一家专注于家庭教育的公司，致力于通过线上和线下课程体系帮助家长和孩子共同成长。公司提供多种课程，如520学习法和学霸二阶计划，旨在帮助孩子建立自信、改善学习习惯、提高学习能力，并培养独立思考和创新能力。\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"data": {"text/plain": ["{'input': '扶鹰是做什么的',\n", " 'output': '扶鹰是一家专注于家庭教育的公司，致力于通过线上和线下课程体系帮助家长和孩子共同成长。公司提供多种课程，如520学习法和学霸二阶计划，旨在帮助孩子建立自信、改善学习习惯、提高学习能力，并培养独立思考和创新能力。'}"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["agent_executor.invoke({\"input\": \"扶鹰是做什么的\"})"]}, {"cell_type": "code", "execution_count": 63, "id": "06e4a2a3-8738-47d7-b8e0-8803c1e71c87", "metadata": {}, "outputs": [], "source": ["from langgraph.graph import MessagesState\n", "\n", "class AgentState(MessagesState):\n", "    next: str"]}, {"cell_type": "code", "execution_count": 57, "id": "731a3a48-f4e3-438f-9a1d-bb999ecfa6cb", "metadata": {}, "outputs": [], "source": ["def chat1(state: AgentState):\n", "    messages = state['messages']"]}, {"cell_type": "code", "execution_count": 58, "id": "b5d7876b-8332-431d-9d11-d9862e5c7809", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ed31b09a-3ab3-4f53-90d9-a26ee1703ce0", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 5}
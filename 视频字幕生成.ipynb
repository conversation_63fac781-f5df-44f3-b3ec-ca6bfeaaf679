{"cells": [{"cell_type": "code", "execution_count": null, "id": "887f9e3a", "metadata": {}, "outputs": [], "source": ["!ffmpeg -i 02.mp4 -q:a 0 -map a audio.mp3\n", "!ffmpeg -i 02.mp4 -vf \"subtitles=002_zh.srt\" -c:a copy 002.mp4"]}, {"cell_type": "code", "execution_count": null, "id": "fb1d5f97", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "    \"task_id\": \"8c139280-3637-4633-b6cf-39b69e6660e6\",\n", "    \"task_status\": \"SUCCEEDED\",\n", "    \"submit_time\": \"2025-08-27 11:42:59.216\",\n", "    \"scheduled_time\": \"2025-08-27 11:42:59.253\",\n", "    \"end_time\": \"2025-08-27 11:51:22.221\",\n", "    \"results\": [\n", "        {\n", "            \"file_url\": \"https://*************/file/audio.mp3\",\n", "            \"transcription_url\": \"https://dashscope-result-bj.oss-cn-beijing.aliyuncs.com/prod/paraformer-v2/20250827/11%3A51/345ff0b3-c917-4bee-8d06-554dbbcace59-1.json?Expires=1756353082&OSSAccessKeyId=LTAI5tQZd8AEcZX6KZV4G8qL&Signature=YKlVTCdTkf6isJ7m2qXiMIhQ4rw%3D\",\n", "            \"subtask_status\": \"SUCCEEDED\"\n", "        }\n", "    ],\n", "    \"task_metrics\": {\n", "        \"TOTAL\": 1,\n", "        \"SUCCEEDED\": 1,\n", "        \"FAILED\": 0\n", "    }\n", "}\n", "transcription done!\n"]}], "source": ["from http import HTTPStatus\n", "from dashscope.audio.asr import Transcription\n", "import json\n", "\n", "# 若没有将API Key配置到环境变量中，需将下面这行代码注释放开，并将apiKey替换为自己的API Key\n", "import dashscope\n", "dashscope.api_key = \"sk-b536c8e6e4ae48d7a52d2bb171ad4119\"\n", "\n", "task_response = Transcription.async_call(\n", "    model='paraformer-v2',\n", "    file_urls=['https://*************/file/audio.mp3'],\n", "    language_hints=['ja']  # “language_hints”只支持paraformer-v2模型\n", ")\n", "\n", "transcribe_response = Transcription.wait(task=task_response.output.task_id)\n", "if transcribe_response.status_code == HTTPStatus.OK:\n", "    # 下载结果文件\n", "    print(json.dumps(transcribe_response.output, indent=4, ensure_ascii=False))"]}, {"cell_type": "code", "execution_count": null, "id": "648491fc", "metadata": {}, "outputs": [], "source": ["url = transcribe_response.output.results[0]['transcription_url']\n", "# 下载文件\n", "import requests\n", "response = requests.get(url)\n", "file_path = 'video/002'\n", "with open(file_path + '.json', 'wb') as f:\n", "    f.write(response.content)"]}, {"cell_type": "code", "execution_count": 8, "id": "f20f93b6", "metadata": {}, "outputs": [], "source": ["with open(file_path, 'r', encoding='utf-8') as f:\n", "    data = json.load(f)\n", "\n", "subtitles = []\n", "for sentence in data['transcripts'][0]['sentences']:\n", "    subtitle = {\n", "        'begin_time': sentence['begin_time'],\n", "        'end_time': sentence['end_time'],\n", "        'text': sentence['text'],\n", "        'sentence_id': sentence['sentence_id']\n", "    }\n", "    subtitles.append(subtitle)"]}, {"cell_type": "code", "execution_count": 10, "id": "b80c59ab", "metadata": {}, "outputs": [], "source": ["def format_time(milliseconds):\n", "    \"\"\"\n", "    Convert milliseconds to SRT time format (HH:MM:SS,mmm)\n", "    \"\"\"\n", "    hours = milliseconds // 3600000\n", "    milliseconds %= 3600000\n", "    minutes = milliseconds // 60000\n", "    milliseconds %= 60000\n", "    seconds = milliseconds // 1000\n", "    milliseconds %= 1000\n", "    \n", "    return f\"{hours:02d}:{minutes:02d}:{seconds:02d},{milliseconds:03d}\"\n", "\n", "with open(file_path + '.srt', 'w', encoding='utf-8') as f:\n", "    for i, subtitle in enumerate(subtitles, 1):\n", "        # Convert time from milliseconds to SRT format (HH:MM:SS,mmm)\n", "        begin_time = format_time(subtitle['begin_time'])\n", "        end_time = format_time(subtitle['end_time'])\n", "        \n", "        f.write(f\"{i}\\n\")\n", "        f.write(f\"{begin_time} --> {end_time}\\n\")\n", "        f.write(f\"{subtitle['text']}\\n\")\n", "        f.write(\"\\n\")"]}, {"cell_type": "code", "execution_count": null, "id": "df8e453f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "dd187cca", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "25a6be2a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 5}